// ////////////////////////////
// サービス内容
// ////////////////////////////
.owned-media-service {
  @include owned-media-section($owned-media-bg-color);
  @include owned-media-section-bg-gradient($owned-media-base-color, $owned-media-mint-light, 700px);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  // サービスリスト
  &__list {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 50px;
    width: 100%;
  }

  // サービスアイテム
  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: $owned-media-service-item-gap;
    padding: $owned-media-service-item-padding;
    background-color: $owned-media-white;
    border-radius: $owned-media-service-item-border-radius;
    box-shadow: 0 0 14px $owned-media-service-shadow;

    @include service-item-arrow;
  }

  // 上段エリア（番号、タイトル、アイコン）
  &__item-top {
    position: relative;
    display: flex;
    gap: $owned-media-service-item-top-gap;
    align-items: flex-start;
    padding-right: $owned-media-service-item-top-padding-right;
  }

  // 下段エリア（本文テキスト）
  &__item-bottom {
    width: 100%;
    padding: 0 50px;
  }

  // 番号エリア
  &__item-number {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: $owned-media-service-number-gap;
    align-items: center;

    // プログレスマーク
    &::before {
      @include service-progress-mark;
    }
  }

  &__item-number-main {
    font-size: $owned-media-service-number-main-font-size;
    font-weight: 500;
    line-height: $owned-media-service-number-main-line-height;
    color: $owned-media-mint-border;
    letter-spacing: $owned-media-service-number-main-letter-spacing;
  }

  &__item-number-sub {
    position: relative;
    font-family: 'SF Pro Text', helvetica, sans-serif;
    font-size: $owned-media-service-number-sub-font-size;
    font-weight: 500;
    color: $owned-media-mint-border;
    letter-spacing: $owned-media-service-number-sub-letter-spacing;

    @include service-number-decoration;
  }

  // コンテンツエリア
  &__item-content {
    position: relative;
    flex: 1;
    max-width: $owned-media-service-icon-content-max-width;
    min-height: 70px;

    &::before {
      position: absolute;
      bottom: -10px;
      left: 0;
      display: block;
      width: 80%;
      height: 5px;
      content: '';
      background-color: #e7eff3;
    }

    &::after {
      position: absolute;
      right: 20%;
      bottom: -10px;
      display: block;
      width: 10%;
      height: 5px;
      content: '';
      background-color: $owned-media-mint;
    }
  }

  &__item-title {
    @include service-item-title;

    position: relative;
    display: inline-block;
    text-align: left;

    @include owned-media-mobile {
      flex-direction: column;
      text-align: center;
    }
  }

  &__item-title-line1,
  &__item-title-line2 {
    display: inline-block;
    text-align: left;

    @include owned-media-mobile {
      width: 100%;
    }
  }

  &__item-title-normal {
    color: $owned-media-text-color;
    letter-spacing: $owned-media-service-title-normal-letter-spacing;
  }

  &__item-title-accent {
    font-size: $owned-media-service-title-accent-font-size;
    color: $owned-media-primary-color;
    letter-spacing: $owned-media-service-title-accent-letter-spacing;
  }

  &__item-description {
    @include service-item-description;
  }

  // アイコン
  &__item-icon {
    @include service-icon-base;

    &--01 {
      @include service-icon-01;
    }

    &--02 {
      @include service-icon-02;
    }

    &--03 {
      @include service-icon-03;
    }

    &--04 {
      @include service-icon-04;
    }

    &--05 {
      @include service-icon-05;
    }

    &--06 {
      @include service-icon-06;
    }

    &--07 {
      @include service-icon-07;
    }
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    min-height: auto;
    padding: $owned-media-spacing-xl $owned-media-spacing-small;

    @include owned-media-section-bg-gradient(
      $owned-media-base-color,
      $owned-media-mint-light,
      420px
    );

    &__container {
      padding: 0;
    }

    &__title {
      font-size: $owned-media-service-mobile-title-font-size;
      letter-spacing: $owned-media-service-mobile-title-letter-spacing;
    }

    &__subtitle-text {
      font-size: $owned-media-service-mobile-subtitle-font-size;
      letter-spacing: $owned-media-service-mobile-subtitle-letter-spacing;
    }

    &__list {
      gap: $owned-media-service-mobile-gap;
      padding: 0;
    }

    &__item {
      gap: $owned-media-service-mobile-item-gap;
      padding: $owned-media-service-mobile-item-padding;
      border-radius: $owned-media-service-mobile-item-border-radius;

      &:not(:last-child) {
        &::after {
          display: none; // モバイルでは矢印を非表示
        }
      }
    }

    &__item-top {
      flex-direction: column;
      gap: $owned-media-service-mobile-item-top-gap;
      align-items: center;
      padding-right: 0;
      margin-right: 0;
    }

    &__item-bottom {
      padding: 0;
      text-align: center;
    }

    &__item-number {
      flex-direction: column;
      gap: 0;
      align-items: center;
      width: 100%;
      padding-top: $owned-media-service-mobile-number-padding-top; // プログレスマーク用のスペース

      // モバイルでもプログレスマークを表示
      &::before {
        @include service-progress-mark-mobile;
      }
    }

    &__item-number-main {
      font-size: $owned-media-service-mobile-number-main-font-size;
      line-height: $owned-media-service-mobile-number-main-line-height;
    }

    &__item-number-sub {
      margin-top: 0;
      font-size: $owned-media-service-mobile-number-sub-font-size;
      letter-spacing: $owned-media-service-mobile-number-sub-letter-spacing;

      @include service-number-decoration-mobile;
    }

    &__item-content {
      flex: none;
      width: 100%;
      max-width: none;
      padding-top: 0;

      &::before {
        width: 100%;
        height: 7px;
      }

      &::after {
        right: 0;
        height: 7px;
      }
    }

    &__item-title {
      display: flex;
      flex-direction: column;
      align-items: center;
      width: 100%;
      margin-bottom: $owned-media-service-mobile-content-margin-bottom;
      font-size: $owned-media-service-mobile-item-title-font-size;
      line-height: $owned-media-service-mobile-item-title-line-height;
    }

    &__item-title-normal {
      text-align: center;
      letter-spacing: 0;
    }

    &__item-title-accent {
      font-size: $owned-media-service-mobile-title-accent-font-size;
      text-align: center;
      letter-spacing: 0;
    }

    &__item-description {
      font-size: 20px;
      line-height: $owned-media-service-mobile-description-line-height;
      letter-spacing: $owned-media-service-mobile-description-letter-spacing;
    }

    &__item-icon {
      position: static;
      width: 100%;
      max-width: $owned-media-service-mobile-icon-max-width;
      height: auto;
      margin: 0 auto;
    }

    &__flow-bg {
      display: none;
    }
  }
}
