// 発見パートセクション
.owned-media-discovery {
  @include owned-media-section;

  position: relative;
  width: 100%;

  // @include discovery-background-grid;
  @include discovery-diagonal-decoration;

  background-color: #a0c1bb;

  &__container {
    @include owned-media-container;

    width: 100%;
    max-width: $owned-media-discovery-container-max-width;
    padding-top: $owned-media-discovery-container-padding-top;
  }

  // 見出し
  &__header {
    width: 100%;
    margin: 0 auto;
  }

  &__title {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
  }

  &__title-prefix {
    @include discovery-title-text(
      $owned-media-discovery-title-prefix-font-size,
      $owned-media-text-color,
      $owned-media-discovery-title-prefix-letter-spacing
    );

    position: relative;
  }

  &__title-wrapper {
    position: relative;
    display: flex;
  }

  &__title-main {
    position: relative;
    top: -18px;
    margin: 0 $owned-media-spacing-small;
    margin-right: 0;
    white-space: nowrap;
    transform: $owned-media-discovery-title-main-transform;
    -webkit-text-stroke: $owned-media-discovery-title-main-stroke-width $owned-media-text-color;
    paint-order: stroke;

    @include discovery-title-text(
      $owned-media-discovery-title-main-font-size,
      $owned-media-white,
      $owned-media-discovery-title-main-letter-spacing,
      900,
      $owned-media-discovery-title-main-line-height
    );
    @include discovery-title-main-accent(
      $owned-media-discovery-title-main-accent-width,
      $owned-media-discovery-title-main-accent-height,
      $owned-media-discovery-title-main-accent-right,
      $owned-media-discovery-title-main-accent-transform
    );
  }

  &__title-suffix {
    @include discovery-title-text(
      $owned-media-discovery-title-suffix-font-size,
      $owned-media-text-color,
      $owned-media-discovery-title-suffix-letter-spacing,
      700
    );

    position: relative;
    margin-left: $owned-media-spacing-small;
  }

  &__accent {
    position: absolute;
    top: $owned-media-discovery-accent-top;
    right: $owned-media-discovery-accent-right;
    width: $owned-media-discovery-accent-width;
    height: $owned-media-discovery-accent-height;
  }

  // コンテンツエリア
  &__content {
    position: relative;
    width: 100%;
    height: $owned-media-discovery-content-height;
  }

  // イラストと吹き出しのコンテナ
  &__illustration-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
  }

  // 中央のイラスト
  &__illustration {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 10;
    transform: translate(-50%, -50%);
  }

  &__person {
    position: relative;
    top: $owned-media-discovery-person-top;
    left: $owned-media-discovery-person-left;
    width: auto;
    max-width: $owned-media-discovery-person-max-width;
    height: auto;
    max-height: $owned-media-discovery-person-max-height;
    object-fit: contain;
  }

  &__desk {
    position: absolute;
    top: $owned-media-discovery-desk-top;
    left: $owned-media-discovery-desk-left;
    width: $owned-media-discovery-desk-width;
    height: $owned-media-discovery-desk-height;
    object-fit: contain;
  }

  // 失敗理由の吹き出し
  &__reasons {
    position: relative;
    width: 100%;
    max-width: $owned-media-discovery-reasons-max-width;
    height: 100%;
  }

  &__reason {
    position: absolute;

    &--1 {
      top: $owned-media-discovery-reason-1-top;
      left: $owned-media-discovery-reason-1-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-1-width,
          $owned-media-discovery-reason-1-height,
          'discovery-bubble-1.png',
          $owned-media-discovery-reason-1-transform
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-50%, -80%);
      }
    }

    &--2 {
      top: $owned-media-discovery-reason-2-top;
      left: $owned-media-discovery-reason-2-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-2-width,
          $owned-media-discovery-reason-2-height,
          'discovery-bubble-1.png',
          $owned-media-discovery-reason-2-transform
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-45%, -80%);
      }
    }

    &--3 {
      top: $owned-media-discovery-reason-3-top;
      right: $owned-media-discovery-reason-3-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-3-width,
          $owned-media-discovery-reason-3-height,
          'discovery-bubble-2.png'
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-50%, -80%);
      }
    }

    &--4 {
      top: $owned-media-discovery-reason-4-top;
      left: $owned-media-discovery-reason-4-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-4-width,
          $owned-media-discovery-reason-4-height,
          'discovery-bubble-3.png'
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-50%, -45%);
      }
    }

    &--5 {
      top: $owned-media-discovery-reason-5-top;
      left: $owned-media-discovery-reason-5-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-5-width,
          $owned-media-discovery-reason-5-height,
          'discovery-bubble-4.png',
          $owned-media-discovery-reason-5-transform
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-50%, -35%);
      }
    }

    &--6 {
      top: $owned-media-discovery-reason-6-top;
      left: $owned-media-discovery-reason-6-left;

      .owned-media-discovery__reason-bubble {
        @include discovery-reason-bubble(
          $owned-media-discovery-reason-6-width,
          $owned-media-discovery-reason-6-height,
          'discovery-bubble-1.png',
          $owned-media-discovery-reason-6-transform
        );
      }

      .owned-media-discovery__reason-text {
        @include discovery-reason-text-transform(-55%, -35%);
      }
    }
  }

  &__reason-bubble {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;

    &::before {
      position: absolute;
      top: 0;
      left: 0;
      z-index: 0;
      width: 100%;
      height: 100%;
      content: '';
      background-repeat: no-repeat;
      background-position: center;
      background-size: contain;
    }
  }

  &__reason-text {
    position: absolute;
    top: 50%;
    left: 50%;
    z-index: 2;
    width: $owned-media-discovery-reason-text-width;
    margin: 0;
    font-family: $owned-media-font-family-noto;
    font-size: $owned-media-discovery-reason-text-font-size;
    line-height: $owned-media-discovery-reason-text-line-height;
    text-align: center;
    transform: translate(-50%, -50%);
  }

  &__reason-strong {
    font-weight: 900;
    color: $owned-media-discovery-text-strong;
  }

  &__reason-weak {
    font-weight: 700;
    color: $owned-media-text-color-sumikuro;
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    height: auto;
    padding: $owned-media-discovery-mobile-padding;

    &__container {
      align-items: center;
      padding: $owned-media-discovery-mobile-container-padding;
    }

    &__bg-image {
      top: $owned-media-discovery-mobile-bg-image-top;
      height: auto;
    }

    &__arrow {
      top: $owned-media-discovery-mobile-arrow-top;
      width: $owned-media-discovery-mobile-arrow-width;
      height: $owned-media-discovery-mobile-arrow-height;
    }

    &__header {
      max-width: 100%;
      padding-top: $owned-media-discovery-mobile-header-padding-top;
    }

    &__title {
      position: relative;
      flex-direction: column;
      gap: $owned-media-discovery-mobile-title-gap;
      align-items: center;
      width: 100%;
    }

    &__title-wrapper {
      display: flex;
      flex-direction: row;
      align-items: center;
    }

    &__title-prefix {
      display: block;
      font-size: $owned-media-discovery-mobile-title-prefix-font-size;
    }

    &__title-main {
      position: static;
      display: block;
      font-size: $owned-media-discovery-mobile-title-main-font-size;
      letter-spacing: $owned-media-discovery-mobile-title-main-letter-spacing;
      -webkit-text-stroke: $owned-media-discovery-mobile-title-main-stroke-width
        $owned-media-text-color;
      text-stroke: $owned-media-discovery-mobile-title-main-stroke-width $owned-media-text-color;
      transform: $owned-media-discovery-title-main-transform;

      &::before {
        position: absolute;
        top: $owned-media-discovery-mobile-title-main-accent-top;
        right: $owned-media-discovery-mobile-title-main-accent-right;
        width: $owned-media-discovery-mobile-title-main-accent-size;
        height: $owned-media-discovery-mobile-title-main-accent-size;
        content: '';
        background-image: url('#{$owned-media-image-path}/discovery-accent.png');
        background-repeat: no-repeat;
      }
    }

    &__title-suffix {
      display: block;
      margin-left: 0;
      font-size: $owned-media-discovery-mobile-title-suffix-font-size;
      letter-spacing: $owned-media-discovery-mobile-title-suffix-letter-spacing;

      &-small {
        display: $owned-media-discovery-mobile-title-suffix-small-font-size;
      }
    }

    &__accent {
      position: static;
      display: inline-block;
      width: $owned-media-discovery-mobile-accent-width;
      height: $owned-media-discovery-mobile-accent-height;
      margin-left: $owned-media-discovery-mobile-accent-margin-left;
      vertical-align: middle;
    }

    &__content {
      position: static;
      max-width: 100%;
      height: auto;
      margin-top: $owned-media-discovery-mobile-content-margin-top;
    }

    &__illustration-container {
      position: static;
      flex-direction: column;
      height: auto;
    }

    &__illustration {
      position: static;
      height: $owned-media-discovery-mobile-illustration-height;
      margin-bottom: 0;
      text-align: center;
      transform: none;
    }

    &__person {
      top: $owned-media-discovery-mobile-person-top;
      left: $owned-media-discovery-mobile-person-left;
      width: $owned-media-discovery-mobile-person-width;
      height: $owned-media-discovery-mobile-person-height;
    }

    &__desk {
      position: absolute;
      top: $owned-media-discovery-mobile-desk-top;
      left: $owned-media-discovery-mobile-desk-left;
      width: $owned-media-discovery-mobile-desk-width;
      height: $owned-media-discovery-mobile-desk-height;
    }

    &__reasons {
      position: static;
      display: grid;
      grid-template-columns: 1fr;
      gap: $owned-media-discovery-mobile-reasons-gap;
      width: 100%;
      padding: $owned-media-discovery-mobile-reasons-padding;
    }

    &__reason {
      position: static !important;
    }

    &__reason-bubble {
      @include discovery-mobile-bubble;
    }

    &__reason-text {
      position: static !important; // スマホ版では通常のフローに戻す
      top: auto !important;
      left: auto !important;
      width: 100% !important;
      font-size: $owned-media-discovery-mobile-reason-text-font-size;
      transform: none !important;
    }

    &__reason-weak {
      font-size: $owned-media-discovery-mobile-reason-weak-font-size;
    }
  }
}
