// ==================================================
// 共通コンポーネント
// ==================================================

// セクションヘッダー
.c-section-header {
  text-align: center;
  margin-bottom: 80px;

  @include owned-media-mobile {
    margin-bottom: 48px;
  }

  &__title {
    font-size: clamp(52px, 10vw, 80px);
    font-weight: 700;
    color: $owned-media-mint;
    margin-bottom: 24px;

    @include owned-media-mobile {
      font-size: 28px;
      margin-bottom: 12px;
    }
  }

  &__subtitle {
    position: relative;
    display: inline-flex;
    align-items: center;
    gap: 20px;
    font-size: 38px;
    font-weight: 500;
    color: $owned-media-mint;
    letter-spacing: 0.38px;

    &::before,
    &::after {
      content: '';
      display: block;
      width: 15px;
      height: 4px;
      background-color: $owned-media-mint;
      border-radius: 2px;
    }

    @include owned-media-mobile {
      font-size: 24px;
      gap: 8px;

      &::before,
      &::after {
        height: 2px;
      }
    }
  }
}

// ボタンコンポーネント
.c-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24px 56px;
  font-size: 38px;
  font-weight: 500;
  text-decoration: none;
  border-radius: 160px;
  transition: all 0.3s ease;
  box-shadow: 0 9px 19px rgba(82, 134, 120, 0.13);

  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 24px rgba(82, 134, 120, 0.2);
  }

  // アウトラインタイプ
  &--outline {
    background-color: $owned-media-base-color;
    border: 9px solid $owned-media-primary-color;
    color: $owned-media-primary-color;

    &:hover {
      background-color: $owned-media-primary-color;
      color: $owned-media-white;
    }
  }

  // 塗りつぶしタイプ
  &--filled {
    background-color: $owned-media-primary-color;
    border: 9px solid $owned-media-primary-color;
    color: $owned-media-white;

    &:hover {
      background-color: $owned-media-base-color;
      color: $owned-media-primary-color;
    }
  }

  @include owned-media-mobile {
    width: 100%;
    padding: 10px 20px;
    font-size: clamp(12px, 4vw, 22px);
    border-width: 2px;
    border-radius: 90px;
  }
}

// カードコンポーネント
.c-card {
  background-color: $owned-media-white;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);

  @include owned-media-mobile {
    padding: 20px;
    border-radius: 12px;
  }

  &__title {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 16px;
    color: $owned-media-text-color;

    @include owned-media-mobile {
      font-size: 18px;
      margin-bottom: 12px;
    }
  }

  &__content {
    font-size: 16px;
    line-height: 1.6;
    color: $owned-media-text-color;

    @include owned-media-mobile {
      font-size: 14px;
    }
  }
}