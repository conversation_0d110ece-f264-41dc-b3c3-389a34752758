// ==================================================
// 変数定義
// ==================================================
$owned-media-primary-color: #fa6b58;
$owned-media-bg-color: #f9f9f9;
$owned-media-text-color: #333;
$owned-media-text-color-base: #333;
$owned-media-text-color-sumikuro: #5f6061;
$owned-media-white: #fff;
$owned-media-base-color: #f9f9f9;
$owned-media-light-gray: #f9f9f9;
$owned-media-mint: #3ab795;
$owned-media-mint-light: #b1e2d5;
$owned-media-mint-border: #7dc8b6;
$owned-media-mint-dash: #c9e9ed;
$owned-media-discovery-accent: #a0c1bb;
$owned-media-discovery-text-strong: #3c8b86;
$owned-media-discovery-mobile-bg: #e6edf1;
$owned-media-discovery-mobile-shadow: rgb(135 159 170 / 90%);
$owned-media-merit-shadow: rgb(90 134 151 / 50%);
$owned-media-merit-gradient: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
$owned-media-service-shadow: rgb(67 226 184 / 60%);
$owned-media-service-progress-gray: #d9d9d9;
$owned-media-success-marker: #fff54b;
$owned-media-support-title-bg: #5f6061;
$owned-media-support-images-bg: #e1ede8;

// フォント設定
// Noto Sans JP
$owned-media-font-family-noto: 'Noto Sans JP', helvetica, sans-serif;

// Yu Gothic
$owned-media-font-family-yu:
  'Yu Gothic', 'YuGothic', 'Hiragino Kaku Gothic ProN', 'Hiragino Sans', meiryo, sans-serif;

// ブレークポイント
$owned-media-breakpoint-xl: 1200px;
$owned-media-breakpoint-lg: 1000px;
$owned-media-breakpoint-md: 768px;
$owned-media-breakpoint-sm: 480px;

// FVセクション用フォントサイズ変数
$owned-media-fv-catch-font-size: clamp(18px, 2.5vw, 24px);
$owned-media-fv-pill-font-size: clamp(24px, 3vw, 30px);
$owned-media-fv-message-strong-font-size: clamp(100px, 10vw, 130px);
$owned-media-fv-message-accent-font-size: clamp(80px, 8vw, 100px);
$owned-media-fv-message-normal-font-size: clamp(60px, 7vw, 70px);
$owned-media-fv-form-text-font-size: 24px;
$owned-media-fv-mobile-catch-font-size: clamp(18px, 4vw, 32px);
$owned-media-fv-mobile-pill-font-size: clamp(22px, 4vw, 48px);
$owned-media-fv-mobile-message-strong-font-size: clamp(80px, 10vw, 90px);
$owned-media-fv-mobile-message-accent-font-size: clamp(50px, 10vw, 60px);
$owned-media-fv-mobile-message-normal-font-size: clamp(40px, 10vw, 50px);
$owned-media-fv-mobile-form-text-font-size: 20px;

// First Appealセクション専用変数
$owned-media-first-appeal-padding-top: 40px;
$owned-media-first-appeal-container-decoration-top: -80px;
$owned-media-first-appeal-container-decoration-size: 55px;
$owned-media-first-appeal-main-message-margin-bottom: 100px;
$owned-media-first-appeal-main-message-decoration-bottom: -120px;
$owned-media-first-appeal-main-message-decoration-left: 65%;
$owned-media-first-appeal-main-message-decoration-width: 440px;
$owned-media-first-appeal-main-message-decoration-height: 140px;
$owned-media-first-appeal-main-message-line1-margin-right: 8px;
$owned-media-first-appeal-suffix-left: -22px;
$owned-media-first-appeal-sub-message-gap: 10px;
$owned-media-first-appeal-sub-message-line2-padding: 12px 56px;
$owned-media-first-appeal-sub-message-line2-border-radius: 4px;
$owned-media-first-appeal-people-margin-bottom: 40px;
$owned-media-first-appeal-person-height: 180px;
$owned-media-first-appeal-decoration-right: 5%;

// First Appeal - Mobile
$owned-media-first-appeal-mobile-padding: 60px 0 40px;
$owned-media-first-appeal-mobile-container-decoration-top: -120px;
$owned-media-first-appeal-mobile-container-decoration-size: 95px;
$owned-media-first-appeal-mobile-main-message-margin-bottom: 120px;
$owned-media-first-appeal-mobile-main-message-decoration-bottom: -100px;
$owned-media-first-appeal-mobile-main-message-decoration-height: 100px;
$owned-media-first-appeal-mobile-main-message-line1-margin-bottom: 8px;
$owned-media-first-appeal-mobile-sub-message-gap: 20px;
$owned-media-first-appeal-mobile-sub-message-margin-bottom: 20px;
$owned-media-first-appeal-mobile-sub-message-line2-padding: 8px 12px;
$owned-media-first-appeal-mobile-people-gap: 20px;
$owned-media-first-appeal-mobile-people-margin-bottom: 20px;
$owned-media-first-appeal-mobile-people-message-margin-bottom: 20px;
$owned-media-first-appeal-mobile-people-images-gap: 10px;

// First Appeal用フォントサイズ変数
$owned-media-first-appeal-from-font-size: 50px;
$owned-media-first-appeal-connector-font-size: 28px;
$owned-media-first-appeal-to-font-size: 50px;
$owned-media-first-appeal-suffix-font-size: 28px;
$owned-media-first-appeal-sub-message-font-size: clamp(18px, 2.5vw, 24px);
$owned-media-first-appeal-people-message-font-size: clamp(40px, 5vw, 50px);
$owned-media-first-appeal-decoration-width: clamp(150px, 15vw, 219px);
$owned-media-first-appeal-people-gap: clamp(20px, 5vw, 20px);
$owned-media-first-appeal-person-1-width: clamp(35px, 8vw, 47px);
$owned-media-first-appeal-person-2-width: clamp(55px, 12vw, 73px);
$owned-media-first-appeal-person-3-width: clamp(38px, 9vw, 51px);
$owned-media-first-appeal-person-4-width: clamp(49px, 11vw, 65px);

// First Appeal - Mobile用フォントサイズ変数
$owned-media-first-appeal-mobile-from-font-size: clamp(32px, 8vw, 50px);
$owned-media-first-appeal-mobile-connector-font-size: clamp(24px, 6vw, 40px);
$owned-media-first-appeal-mobile-to-font-size: clamp(40px, 10vw, 60px);
$owned-media-first-appeal-mobile-suffix-font-size: clamp(24px, 6vw, 40px);
$owned-media-first-appeal-mobile-sub-message-font-size: clamp(16px, 4vw, 24px);
$owned-media-first-appeal-mobile-sub-message-line2-font-size: clamp(14px, 4vw, 20px);
$owned-media-first-appeal-mobile-people-message-font-size: clamp(30px, 10vw, 40px);
$owned-media-first-appeal-mobile-person-height: clamp(80px, 20vw, 120px);

// Partner Logosセクション専用変数
$owned-media-partner-logos-container-min-height: 120px;
$owned-media-partner-logos-row-gap: 25px;
$owned-media-partner-logos-logo-max-height: 60px;
$owned-media-partner-logos-logo-margin: 0 15px;
$owned-media-partner-logos-mobile-height: 200px;
$owned-media-partner-logos-mobile-row-height: 50px;
$owned-media-partner-logos-mobile-logo-max-height: 45px;
$owned-media-partner-logos-mobile-logo-margin: 0 15px;
$owned-media-partner-logos-mobile-track-gap: 30px;
$owned-media-partner-logos-mobile-container-height: 200px;
$owned-media-partner-logos-mobile-scroll-distance: calc(-100vw - 200px);
$owned-media-partner-logos-pc-scroll-distance: -50%;

// Empathyセクション専用変数
$owned-media-empathy-container-padding: 130px 0;
$owned-media-empathy-header-margin-bottom: 75px;
$owned-media-empathy-title-highlight-height: 39px;
$owned-media-empathy-checklist-padding: 60px;
$owned-media-empathy-checklist-border-width: 8px;
$owned-media-empathy-checklist-decoration-size: 60px;
$owned-media-empathy-item-padding: 28px 0;
$owned-media-empathy-check-icon-size: 38px;
$owned-media-empathy-check-icon-border-width: 7px;
$owned-media-empathy-check-icon-border-radius: 9px;
$owned-media-empathy-check-image-size: 48px;
$owned-media-empathy-item-text-margin-left: 96px;
$owned-media-empathy-item-dash-border-width: 6px;
$owned-media-empathy-mobile-container-padding: 60px 15px;
$owned-media-empathy-mobile-header-margin-bottom: 40px;
$owned-media-empathy-mobile-checklist-padding: 40px 15px;
$owned-media-empathy-mobile-checklist-border-width: 4px;
$owned-media-empathy-mobile-checklist-decoration-size: 31px;
$owned-media-empathy-mobile-item-padding: 30px 0;
$owned-media-empathy-mobile-check-icon-size: 40px;
$owned-media-empathy-mobile-check-icon-border-width: 4px;
$owned-media-empathy-mobile-check-image-size: 50px;
$owned-media-empathy-mobile-item-text-margin-left: 60px;
$owned-media-empathy-mobile-item-dash-border-width: 3px;

// Empathy用フォントサイズ変数
$owned-media-empathy-subtitle-font-size: clamp(32px, 6vw, 48px);
$owned-media-empathy-title-font-size: clamp(40px, 5vw, 56px);
$owned-media-empathy-item-font-size: clamp(24px, 3vw, 24px);
$owned-media-empathy-mobile-subtitle-font-size: clamp(18px, 5vw, 20px);
$owned-media-empathy-mobile-title-font-size: clamp(20px, 6vw, 24px);
$owned-media-empathy-mobile-item-font-size: clamp(16px, 4vw, 20px);

// スペーシング設定
$owned-media-spacing-xs: 8px;
$owned-media-spacing-12: 12px;
$owned-media-spacing-small: 15px;
$owned-media-spacing-small-plus: 18px;
$owned-media-spacing-20: 20px;
$owned-media-spacing-medium: 25px;
$owned-media-spacing-medium-plus: 27px;
$owned-media-spacing-large: 36px;
$owned-media-spacing-32: 32px;
$owned-media-spacing-large-plus: 38px;
$owned-media-spacing-xl: 40px;
$owned-media-spacing-xxl: 60px;
$owned-media-spacing-64: 64px;

// セクションパディング
$owned-media-section-padding-desktop: 40px;
$owned-media-section-padding-mobile: 40px;

// Support セクション専用
$owned-media-support-card-width: 634px;
$owned-media-support-card-height: 679px;
$owned-media-support-card-padding: 46px;
$owned-media-support-card-border-radius: 20px;
$owned-media-support-title-area-height: 67px;
$owned-media-support-title-border-radius: 13px;
$owned-media-support-images-height: 163px;
$owned-media-support-images-border-radius: 10px;
$owned-media-support-images-border-radius-large: 36px;
$owned-media-support-image-border-radius: 14px;
$owned-media-support-content-width: 540px;

// System Support セクション専用
$owned-media-system-support-text-padding-v: 23px;
$owned-media-system-support-text-padding-h: 54px;
$owned-media-system-support-image-padding-v: 100px;
$owned-media-system-support-image-padding-h: 48px;
$owned-media-system-support-message-padding: 56px;
$owned-media-system-support-text-border-radius: 48px;
$owned-media-system-support-image-border-radius: 18px;
$owned-media-system-support-message-border-radius: 70px;
$owned-media-system-support-shadow: 0 0 35px 0 rgb(58 183 149 / 60%);

// System Support モバイル専用
$owned-media-system-support-mobile-text-padding-v: 20px;
$owned-media-system-support-mobile-text-padding-h: 30px;
$owned-media-system-support-mobile-image-padding-v: 40px;
$owned-media-system-support-mobile-image-padding-h: 20px;
$owned-media-system-support-mobile-message-padding-v: 22px;
$owned-media-system-support-mobile-message-padding-h: 16px;
$owned-media-system-support-mobile-text-border-radius: 30px;
$owned-media-system-support-mobile-image-border-radius: 12px;
$owned-media-system-support-mobile-message-border-radius: 16px;
$owned-media-system-support-mobile-shadow: 0 0 8px 0 rgb(58 183 149 / 30%);

// Case Study セクション専用
$owned-media-case-study-title-height: 67px;
$owned-media-case-study-title-gap: 20px;
$owned-media-case-study-title-padding-v: 20px;
$owned-media-case-study-title-padding-h: 48px;
$owned-media-case-study-title-margin-bottom: 80px;
$owned-media-case-study-description-margin-bottom: 32px;
$owned-media-case-study-container-padding: 90px;
$owned-media-case-study-container-margin-top: 48px;
$owned-media-case-study-container-border-width: 6px;
$owned-media-case-study-container-border-radius: 20px;
$owned-media-case-study-graph-margin-top: 48px;

// Case Study - Text colors
$owned-media-case-study-title-text: #fff;
$owned-media-case-study-description-text: #191919;
$owned-media-case-study-period-text: #989898;
$owned-media-case-study-main-text: #191919;
$owned-media-case-study-strong-text: #fa6b58;
$owned-media-case-study-container-border: #3c8b86;
$owned-media-case-study-container-shadow: rgb(60 139 134 / 100%);

// Case Study - Typography sizes
$owned-media-case-study-title-font-size: 45px;
$owned-media-case-study-title-line-height: 28px;
$owned-media-case-study-title-letter-spacing: 0.45px;
$owned-media-case-study-description-font-size: 42px;
$owned-media-case-study-description-line-height: 28px;
$owned-media-case-study-description-letter-spacing: 0.42px;
$owned-media-case-study-period-font-size: 24px;
$owned-media-case-study-period-line-height: 28px;
$owned-media-case-study-period-letter-spacing: 0.24px;
$owned-media-case-study-result-font-size: 46px;
$owned-media-case-study-result-strong-font-size: 74px;

// Case Study - Result container
$owned-media-case-study-result-container-top: 700px;
$owned-media-case-study-result-container-left: 200px;
$owned-media-case-study-result-container-padding: 24px;
$owned-media-case-study-result-container-border-width: 2px;
$owned-media-case-study-result-container-border-radius: 20px;
$owned-media-case-study-result-container-divider-width: 18px;
$owned-media-case-study-result-container-divider-height: 18px;
$owned-media-case-study-result-container-top-padding-bottom: 16px;
$owned-media-case-study-result-container-top-margin-bottom: 16px;
$owned-media-case-study-result-container-divider-border-width: 3px;
$owned-media-case-study-result-text-font-size: 28px;
$owned-media-case-study-result-bottom-height: 32px;
$owned-media-case-study-result-normal-font-size: 24px;
$owned-media-case-study-result-small-font-size: 19px;
$owned-media-case-study-result-strong-font-size: 53px;
$owned-media-case-study-design-box-size: 48px;
$owned-media-case-study-design-box-padding: 4px;
$owned-media-case-study-design-box-margin: 8px;
$owned-media-case-study-design-box-border-radius: 8px;
$owned-media-case-study-design-text-lg-font-size: 28px;
$owned-media-case-study-design-text-md-font-size: 24px;

// Case Study - Mobile
$owned-media-case-study-mobile-padding-top: 80px;
$owned-media-case-study-mobile-padding-bottom: 320px;
$owned-media-case-study-mobile-container-padding: 20px;
$owned-media-case-study-mobile-header-margin-bottom: 60px;
$owned-media-case-study-mobile-title-padding: 22px;
$owned-media-case-study-mobile-title-margin-bottom: 40px;
$owned-media-case-study-mobile-title-font-size: 22px;
$owned-media-case-study-mobile-title-line-height: 1.2;
$owned-media-case-study-mobile-description-margin-bottom: 20px;
$owned-media-case-study-mobile-container-max-width: 336px;
$owned-media-case-study-mobile-container-height: 439px;
$owned-media-case-study-mobile-container-padding: 40px 20px;
$owned-media-case-study-mobile-container-margin-top: 220px;
$owned-media-case-study-mobile-container-border-width: 4px;
$owned-media-case-study-mobile-container-border-radius: 15px;
$owned-media-case-study-mobile-container-shadow: rgb(60 139 134 / 20%);
$owned-media-case-study-mobile-wrapper-top: -190px;
$owned-media-case-study-mobile-wrapper-padding: 20px;
$owned-media-case-study-mobile-wrapper-border-width: 2px;
$owned-media-case-study-mobile-wrapper-border-radius: 15px;
$owned-media-case-study-mobile-wrapper-shadow: rgb(60 139 134 / 20%);
$owned-media-case-study-mobile-wrapper-divider-size: 20px;
$owned-media-case-study-mobile-wrapper-divider-left: 30%;
$owned-media-case-study-mobile-result-font-size: 24px;
$owned-media-case-study-mobile-result-strong-font-size: 32px;
$owned-media-case-study-mobile-description-height: 180px;
$owned-media-case-study-mobile-result-container-top: 430px;
$owned-media-case-study-mobile-result-container-height: 200px;
$owned-media-case-study-mobile-result-container-padding: 20px;
$owned-media-case-study-mobile-result-container-margin-top: 30px;
$owned-media-case-study-mobile-result-container-border-width: 2px;
$owned-media-case-study-mobile-result-container-border-radius: 15px;
$owned-media-case-study-mobile-result-container-top-padding-bottom: 8px;
$owned-media-case-study-mobile-result-container-text-font-size: 24px;
$owned-media-case-study-mobile-result-container-gap: 8px;
$owned-media-case-study-mobile-result-normal-font-size: 20px;
$owned-media-case-study-mobile-result-small-font-size: 16px;
$owned-media-case-study-mobile-result-strong-font-size: 32px;
$owned-media-case-study-mobile-design-box-size: 42px;
$owned-media-case-study-mobile-design-box-padding: 2px;
$owned-media-case-study-mobile-design-box-margin: 4px;
$owned-media-case-study-mobile-design-box-border-radius: 6px;
$owned-media-case-study-mobile-design-box-bottom: -8px;
$owned-media-case-study-mobile-design-text-lg-font-size: 28px;
$owned-media-case-study-mobile-design-text-md-font-size: 20px;

// Contract Flow セクション専用
$owned-media-contract-flow-content-gap: 72px;
$owned-media-contract-flow-step-gap: 32px;
$owned-media-contract-flow-step-min-height: 254px;
$owned-media-contract-flow-step-padding: 48px;
$owned-media-contract-flow-step-padding-right: 0;
$owned-media-contract-flow-step-border-radius: 20px;
$owned-media-contract-flow-step-shadow: 5px 5px 8px rgb(0 0 0 / 30%);
$owned-media-contract-flow-arrow-bottom: -28px;
$owned-media-contract-flow-arrow-left: 48%;
$owned-media-contract-flow-arrow-size: 56px;
$owned-media-contract-flow-number-bg: #7fd5bf;
$owned-media-contract-flow-number-padding-top: 18px;
$owned-media-contract-flow-number-padding-right: 48px;
$owned-media-contract-flow-number-padding-bottom: 18px;
$owned-media-contract-flow-number-padding-left: 32px;
$owned-media-contract-flow-number-font-size: 40px;
$owned-media-contract-flow-number-border-radius: 75px;
$owned-media-contract-flow-description-gap: 24px;
$owned-media-contract-flow-description-title-font-size: 54px;
$owned-media-contract-flow-description-text-font-size: 24px;
$owned-media-contract-flow-icon-width: 194px;

// Contract Flow - Mobile
$owned-media-contract-flow-mobile-padding: 60px;
$owned-media-contract-flow-mobile-container-padding: 20px;
$owned-media-contract-flow-mobile-header-margin-bottom: 60px;
$owned-media-contract-flow-mobile-content-gap: 40px;
$owned-media-contract-flow-mobile-step-gap: 30px;
$owned-media-contract-flow-mobile-step-padding: 30px 38px;
$owned-media-contract-flow-mobile-step-border-radius: 8px;
$owned-media-contract-flow-mobile-step-shadow: 0 0 4px 0 rgb(0 0 0 / 8%);
$owned-media-contract-flow-mobile-number-top: -60px;
$owned-media-contract-flow-mobile-number-left: -60px;
$owned-media-contract-flow-mobile-number-size: 120px;
$owned-media-contract-flow-mobile-number-padding: 15px 20px;
$owned-media-contract-flow-mobile-number-font-size: 60px;
$owned-media-contract-flow-mobile-number-text-right: 20px;
$owned-media-contract-flow-mobile-number-text-bottom: 20px;
$owned-media-contract-flow-mobile-number-text-font-size: 28px;
$owned-media-contract-flow-mobile-description-gap: 20px;
$owned-media-contract-flow-mobile-description-title-font-size: 36px;
$owned-media-contract-flow-mobile-description-title-line-height: 1.3;
$owned-media-contract-flow-mobile-description-text-font-size: 20px;
$owned-media-contract-flow-mobile-description-text-line-height: 1.5;
$owned-media-contract-flow-mobile-icon-width: 120px;

// Discovery セクション専用
$owned-media-discovery-bg-line-opacity: rgb(97 106 109 / 15%);
$owned-media-discovery-diagonal-bg-color: $owned-media-discovery-accent;
$owned-media-discovery-decoration-transform: translateX(-50%) translateY(-50%) rotate(135deg)
  skew(20deg, 20deg);
$owned-media-discovery-container-max-width: 1080px;
$owned-media-discovery-container-padding-top: $owned-media-section-padding-desktop;
$owned-media-discovery-title-prefix-line-height: 1.2;
$owned-media-discovery-title-prefix-letter-spacing: -1.5px;
$owned-media-discovery-title-main-bottom: -32px;
$owned-media-discovery-title-main-line-height: 1.2;
$owned-media-discovery-title-main-letter-spacing: 10px;
$owned-media-discovery-title-main-stroke-width: $owned-media-spacing-xs;
$owned-media-discovery-title-main-transform: rotate(-10deg);
$owned-media-discovery-title-main-accent-right: -40px;
$owned-media-discovery-title-main-accent-width: 60px;
$owned-media-discovery-title-main-accent-height: 66px;
$owned-media-discovery-title-main-accent-transform: rotate(10deg);
$owned-media-discovery-title-suffix-letter-spacing: -1.83px;
$owned-media-discovery-accent-top: -4px;
$owned-media-discovery-accent-right: 250px;
$owned-media-discovery-accent-width: 101px;
$owned-media-discovery-accent-height: 111px;
$owned-media-discovery-content-height: 930px;
$owned-media-discovery-person-top: -200px;
$owned-media-discovery-person-left: -100px;
$owned-media-discovery-person-max-width: 120px;
$owned-media-discovery-person-max-height: 255px;
$owned-media-discovery-desk-top: 0;
$owned-media-discovery-desk-left: -70px;
$owned-media-discovery-desk-width: 230px;
$owned-media-discovery-desk-height: 230px;
$owned-media-discovery-reasons-max-width: 1080px;
$owned-media-discovery-reason-text-width: 80%;

// Discovery - Typography
$owned-media-discovery-title-prefix-font-size: clamp(24px, 5vw, 30px);
$owned-media-discovery-title-main-font-size: clamp(70px, 10vw, 80px);
$owned-media-discovery-title-suffix-font-size: clamp(24px, 5vw, 30px);
$owned-media-discovery-reason-text-font-size: clamp(16px, 2.3vw, 24px);
$owned-media-discovery-reason-text-line-height: 1.16;

// Discovery - Reason Positions
$owned-media-discovery-reason-1-top: -30px;
$owned-media-discovery-reason-1-left: 30px;
$owned-media-discovery-reason-1-width: 400px;
$owned-media-discovery-reason-1-height: 400px;
$owned-media-discovery-reason-1-transform: rotate(-15deg);
$owned-media-discovery-reason-2-top: -28px;
$owned-media-discovery-reason-2-left: 450px;
$owned-media-discovery-reason-2-width: 420px;
$owned-media-discovery-reason-2-height: 420px;
$owned-media-discovery-reason-2-transform: rotate(16deg);
$owned-media-discovery-reason-3-top: 230px;
$owned-media-discovery-reason-3-left: 0px;
$owned-media-discovery-reason-3-width: 380px;
$owned-media-discovery-reason-3-height: 380px;
$owned-media-discovery-reason-4-top: 450px;
$owned-media-discovery-reason-4-left: 500px;
$owned-media-discovery-reason-4-width: 380px;
$owned-media-discovery-reason-4-height: 380px;
$owned-media-discovery-reason-5-top: 550px;
$owned-media-discovery-reason-5-left: 200px;
$owned-media-discovery-reason-5-width: 330px;
$owned-media-discovery-reason-5-height: 330px;
$owned-media-discovery-reason-5-transform: rotate(30deg);
$owned-media-discovery-reason-6-top: 270px;
$owned-media-discovery-reason-6-left: -50px;
$owned-media-discovery-reason-6-width: 380px;
$owned-media-discovery-reason-6-height: 380px;
$owned-media-discovery-reason-6-transform: rotate(-160deg);

// Discovery - Mobile
$owned-media-discovery-mobile-padding: $owned-media-section-padding-mobile 0;
$owned-media-discovery-mobile-container-padding: 0 $owned-media-spacing-small;
$owned-media-discovery-mobile-bg-image-top: 50px;
$owned-media-discovery-mobile-arrow-top: 20px;
$owned-media-discovery-mobile-arrow-width: 120px;
$owned-media-discovery-mobile-arrow-height: 56px;
$owned-media-discovery-mobile-header-padding-top: 80px;
$owned-media-discovery-mobile-title-gap: 8px;
$owned-media-discovery-mobile-title-prefix-font-size: 28px;
$owned-media-discovery-mobile-title-main-font-size: 72px;
$owned-media-discovery-mobile-title-main-letter-spacing: 7.92px;
$owned-media-discovery-mobile-title-main-stroke-width: 7px;
$owned-media-discovery-mobile-title-main-accent-top: 12px;
$owned-media-discovery-mobile-title-main-accent-right: -32px;
$owned-media-discovery-mobile-title-main-accent-size: 32px;
$owned-media-discovery-mobile-title-suffix-font-size: 28px;
$owned-media-discovery-mobile-title-suffix-letter-spacing: 1.2px;
$owned-media-discovery-mobile-title-suffix-small-font-size: 24px;
$owned-media-discovery-mobile-accent-width: 50px;
$owned-media-discovery-mobile-accent-height: 56px;
$owned-media-discovery-mobile-accent-margin-left: 10px;
$owned-media-discovery-mobile-content-margin-top: 60px;
$owned-media-discovery-mobile-illustration-height: 180px;
$owned-media-discovery-mobile-person-top: -180px;
$owned-media-discovery-mobile-person-left: -100px;
$owned-media-discovery-mobile-person-width: 120px;
$owned-media-discovery-mobile-person-height: 253px;
$owned-media-discovery-mobile-desk-top: 250px;
$owned-media-discovery-mobile-desk-left: 30%;
$owned-media-discovery-mobile-desk-width: 200px;
$owned-media-discovery-mobile-desk-height: 198px;
$owned-media-discovery-mobile-reasons-gap: 8px;
$owned-media-discovery-mobile-reasons-padding: 0 10px;
$owned-media-discovery-mobile-bubble-height: 94px;
$owned-media-discovery-mobile-bubble-padding: 10px $owned-media-spacing-small;
$owned-media-discovery-mobile-bubble-border-radius: 47px;
$owned-media-discovery-mobile-bubble-shadow: 0 0 10px $owned-media-discovery-mobile-shadow;
$owned-media-discovery-mobile-reason-text-font-size: 20px;
$owned-media-discovery-mobile-reason-weak-font-size: 18px;

// FAQ セクション専用
$owned-media-faq-item-gap: 32px;
$owned-media-faq-item-padding: 40px;
$owned-media-faq-text-gap: 20px;
$owned-media-faq-question-icon-size: 46px;
$owned-media-faq-question-icon-font-size: 28px;
$owned-media-faq-question-icon-border-radius: 7px;
$owned-media-faq-answer-icon-color: #a6bfc3;
$owned-media-faq-answer-text-padding-left: 72px;
$owned-media-faq-question-font-size: 24px;
$owned-media-faq-answer-font-size: 16px;
$owned-media-faq-border-color: #a6bfc3;

// FAQ - Mobile
$owned-media-faq-mobile-padding: 60px 0 40px;
$owned-media-faq-mobile-item-gap: 30px;
$owned-media-faq-mobile-item-padding: 30px;
$owned-media-faq-mobile-text-gap: 15px;
$owned-media-faq-mobile-icon-size: 36px;
$owned-media-faq-mobile-icon-font-size: 20px;
$owned-media-faq-mobile-icon-border-radius: 5px;
$owned-media-faq-mobile-container-padding: 20px;
$owned-media-faq-mobile-header-margin-bottom: 40px;

// アニメーション設定
$owned-media-animation-duration-fast: 20s;
$owned-media-animation-duration-normal: 30s;
$owned-media-animation-duration-slow: 40s;

// 画像パス
$owned-media-image-path: '../images/s-owned-media';

// FVセクション専用変数
$owned-media-fv-height: auto;
$owned-media-fv-left-width: 50%;
$owned-media-fv-left-min-width: 750px;
$owned-media-fv-left-height: 720px;
$owned-media-fv-container-max-width: 1080px;
$owned-media-fv-form-width: 375px;
$owned-media-fv-form-height: 696px;
$owned-media-fv-form-padding: 20px;
$owned-media-fv-form-border-radius: 20px;
$owned-media-fv-catch-gap: 8px;
$owned-media-fv-catch-margin-bottom: 20px;
$owned-media-fv-catch-line-width: 4px;
$owned-media-fv-catch-line-height: 90px;
$owned-media-fv-catch-line-offset: 64px;
$owned-media-fv-pill-height: 80px;
$owned-media-fv-pill-margin-bottom: 24px;
$owned-media-fv-pill-padding: 12px 24px;
$owned-media-fv-pill-border-width: 5px;
$owned-media-fv-pill-border-radius: 50px;
$owned-media-fv-message-gap: 16px;
$owned-media-fv-mobile-height: 700px;
$owned-media-fv-mobile-catch-line-height: 60px;
$owned-media-fv-mobile-catch-line-offset: 12px;
$owned-media-fv-mobile-pill-height: 60px;
$owned-media-fv-mobile-pill-padding: 12px;
$owned-media-fv-mobile-pill-border-width: 2px;
$owned-media-fv-mobile-message-gap: 16px;
$owned-media-fv-mobile-form-padding: 20px;

// Merit セクション専用変数
// Layout & Sizing
$owned-media-merit-header-margin-bottom: 16px;
$owned-media-merit-decoration-width: 213px;
$owned-media-merit-decoration-height: 213px;
$owned-media-merit-brand-text-height: auto;
$owned-media-merit-brand-text-padding: 8px;
$owned-media-merit-brand-text-radius: 44px;
$owned-media-merit-brand-arrow-size: 18px;
$owned-media-merit-brand-arrow-offset: 6px;
$owned-media-merit-brand-service-top: 4px;
$owned-media-merit-brand-service-left: 169px;
$owned-media-merit-list-gap: 120px;
$owned-media-merit-item-image-size: clamp(487px, 25vw, 567px);
$owned-media-merit-item-image-min-size: clamp(440px, 25vw, 487px);
$owned-media-merit-item-text-max-width: 700px;
$owned-media-merit-number-icon-width: 38px;
$owned-media-merit-number-icon-height: 38px;
$owned-media-merit-number-bg-width: 38px;
$owned-media-merit-number-bg-height: 38px;
$owned-media-merit-number-label-top: 12px;
$owned-media-merit-number-gap: 16px;
$owned-media-merit-image-bg-offset-top: 25%;
$owned-media-merit-image-bg-offset-left: -15%;
$owned-media-merit-image-bg-max-width: 213px;
$owned-media-merit-image-bg-max-height: 213px;

// Typography
$owned-media-merit-brand-name-font-size: 24px;
$owned-media-merit-brand-suffix-font-size: 28px;
$owned-media-merit-brand-service-font-size: 28px;
$owned-media-merit-catch-text-font-size: 38px;
$owned-media-merit-catch-save-font-size: 83px;
$owned-media-merit-title-number-font-size: 160px;
$owned-media-merit-title-suffix-font-size: 86px;
$owned-media-merit-title-main-font-size: 86px;
$owned-media-merit-title-accent-font-size: 106px;
$owned-media-merit-title-accent-letter-spacing: -12px;
$owned-media-merit-number-text-font-size: 56px;
$owned-media-merit-number-text-letter-spacing: 1.6px;
$owned-media-merit-number-label-font-size: 14px;
$owned-media-merit-item-title-font-size: 45px;
$owned-media-merit-item-title-main-font-size: 24px;
$owned-media-merit-item-title-sub-font-size: 24px;
$owned-media-merit-item-title-accent-font-size: 24px;
$owned-media-merit-item-desc-font-size: 16px;
$owned-media-merit-item-desc-letter-spacing: 0.24px;
$owned-media-merit-item-desc-line-height: 1.9;

// Decoration Positions
$owned-media-merit-decoration-1-top: 531px;
$owned-media-merit-decoration-1-left: 273px;
$owned-media-merit-decoration-2-top: 1195px;
$owned-media-merit-decoration-2-right: 297px;
$owned-media-merit-decoration-3-top: 1910px;
$owned-media-merit-decoration-3-left: 273px;
$owned-media-merit-decoration-4-top: 2555px;
$owned-media-merit-decoration-4-right: 297px;
$owned-media-merit-decoration-5-top: 3270px;
$owned-media-merit-decoration-5-left: 273px;

// Mobile Specific
$owned-media-merit-mobile-header-margin-bottom: 60px;
$owned-media-merit-mobile-brand-text-height: 60px;
$owned-media-merit-mobile-brand-text-padding: 8px 20px;
$owned-media-merit-mobile-brand-text-font-size: 20px;
$owned-media-merit-mobile-brand-suffix-font-size: 18px;
$owned-media-merit-mobile-brand-service-font-size: 18px;
$owned-media-merit-mobile-brand-arrow-size: 24px;
$owned-media-merit-mobile-brand-arrow-offset: 12px;
$owned-media-merit-mobile-catch-margin-bottom: 20px;
$owned-media-merit-mobile-catch-text-font-size: clamp(18px, 3vw, 22px);
$owned-media-merit-mobile-catch-divider-width: 100px;
$owned-media-merit-mobile-catch-divider-height: 3px;
$owned-media-merit-mobile-catch-wo-font-size: clamp(18px, 3vw, 22px);
$owned-media-merit-mobile-catch-save-font-size: clamp(32px, 4vw, 48px);
$owned-media-merit-mobile-title-number-font-size: clamp(60px, 12vw, 80px);
$owned-media-merit-mobile-title-suffix-font-size: clamp(32px, 6vw, 42px);
$owned-media-merit-mobile-title-main-font-size: clamp(32px, 6vw, 42px);
$owned-media-merit-mobile-title-accent-font-size: clamp(42px, 8vw, 52px);
$owned-media-merit-mobile-title-text-gap: 5px;
$owned-media-merit-mobile-list-gap: 60px;
$owned-media-merit-mobile-item-content-gap: 30px;
$owned-media-merit-mobile-item-image-padding: 18px;
$owned-media-merit-mobile-item-image-bg-offset-right: -30%;
$owned-media-merit-mobile-item-image-bg-offset-right-alt: -25%;
$owned-media-merit-mobile-item-header-margin-bottom: 30px;
$owned-media-merit-mobile-number-text-font-size: clamp(60px, 12vw, 80px);
$owned-media-merit-mobile-number-icon-width: 80px;
$owned-media-merit-mobile-number-icon-height: 80px;
$owned-media-merit-mobile-number-bg-width: 74px;
$owned-media-merit-mobile-number-bg-height: 80px;
$owned-media-merit-mobile-number-label-top: 20px;
$owned-media-merit-mobile-number-label-font-size: 24px;
$owned-media-merit-mobile-item-title-font-size: clamp(20px, 4vw, 24px);
$owned-media-merit-mobile-item-title-main-font-size: clamp(24px, 5vw, 28px);
$owned-media-merit-mobile-item-title-sub-font-size: clamp(20px, 4vw, 24px);
$owned-media-merit-mobile-item-title-accent-font-size: clamp(24px, 5vw, 28px);
$owned-media-merit-mobile-item-desc-font-size: clamp(14px, 3vw, 16px);
$owned-media-merit-mobile-item-desc-line-height: 1.7;

// Service セクション専用変数
// ====================================

// Layout & Spacing
$owned-media-service-gap: 100px;
$owned-media-service-item-gap: 28px;
$owned-media-service-item-padding: 40px 56px;
$owned-media-service-item-border-radius: 34px;
$owned-media-service-item-top-padding-right: 200px;
$owned-media-service-item-top-gap: $owned-media-section-padding-mobile;
$owned-media-service-number-gap: $owned-media-spacing-small;
$owned-media-service-arrow-width: 140px;
$owned-media-service-arrow-height: $owned-media-spacing-xxl;
$owned-media-service-arrow-bottom-offset: -$owned-media-spacing-xxl;

// Progress Mark
$owned-media-service-progress-width: 110px;
$owned-media-service-progress-height: 24px;
$owned-media-service-progress-dot-size: 12px;
$owned-media-service-progress-dot-container-size: 24px;
$owned-media-service-progress-line-width: 88px;
$owned-media-service-progress-line-height: 3px;
$owned-media-service-progress-line-top: 11px;
$owned-media-service-progress-line-left: 11px;
$owned-media-service-progress-dot-positions: (0, 15px, 30px, 44px, 59px, 73px, 88px);

// Typography
$owned-media-service-number-main-font-size: clamp(38px, 10vw, 50px);
$owned-media-service-number-main-line-height: 1;
$owned-media-service-number-main-letter-spacing: 1px;
$owned-media-service-number-sub-font-size: 14px;
$owned-media-service-number-sub-line-height: 1;
$owned-media-service-number-sub-letter-spacing: 0.21px;
$owned-media-service-number-sub-margin-top: -$owned-media-spacing-medium;
$owned-media-service-title-font-size: clamp(22px, 2.3vw, 28px);
$owned-media-service-title-normal-letter-spacing: 0.2px;
$owned-media-service-title-accent-font-size: clamp(22px, 2.3vw, 28px);
$owned-media-service-title-accent-letter-spacing: 0.3px;
$owned-media-service-description-font-size: 16px;
$owned-media-service-description-line-height: 1.5;
$owned-media-service-description-letter-spacing: 0.5px;

// Decorative Elements
$owned-media-service-decoration-width: $owned-media-spacing-small;
$owned-media-service-decoration-height: 4px;
$owned-media-service-decoration-border-radius: 2px;
$owned-media-service-decoration-margin: $owned-media-spacing-xs;

// Icon Sizing
$owned-media-service-icon-base-width: clamp(280px, 25vw, 365px);
$owned-media-service-icon-base-height: clamp(200px, 25vw, 250px);
$owned-media-service-icon-top: -160px;
$owned-media-service-icon-right: -10%;
$owned-media-service-icon-content-max-width: 715px;

// Specific Icon Dimensions
$owned-media-service-icon-01-width: clamp(280px, 25vw, 365px);
$owned-media-service-icon-01-height: clamp(200px, 25vw, 250px);
$owned-media-service-icon-02-width: clamp(280px, 25vw, 343px);
$owned-media-service-icon-02-height: clamp(200px, 25vw, 296px);
$owned-media-service-icon-03-width: clamp(280px, 25vw, 385px);
$owned-media-service-icon-03-height: clamp(200px, 25vw, 247px);
$owned-media-service-icon-04-width: clamp(280px, 25vw, 401px);
$owned-media-service-icon-04-height: clamp(200px, 25vw, 296px);
$owned-media-service-icon-05-width: clamp(280px, 25vw, 422px);
$owned-media-service-icon-05-height: clamp(200px, 25vw, 268px);
$owned-media-service-icon-06-width: clamp(280px, 25vw, 439px);
$owned-media-service-icon-06-height: clamp(200px, 25vw, 247px);
$owned-media-service-icon-07-width: clamp(280px, 25vw, 498px);
$owned-media-service-icon-07-height: clamp(200px, 25vw, 223px);

// Mobile Specific Variables
$owned-media-service-mobile-gap: 40px;
$owned-media-service-mobile-item-gap: 30px;
$owned-media-service-mobile-item-padding: 40px 20px;
$owned-media-service-mobile-item-border-radius: 20px;
$owned-media-service-mobile-item-top-gap: 30px;
$owned-media-service-mobile-number-padding-top: 30px;
$owned-media-service-mobile-content-margin-bottom: 15px;

// Mobile Progress Mark
$owned-media-service-mobile-progress-width: 220px;
$owned-media-service-mobile-progress-height: 22px;
$owned-media-service-mobile-progress-dot-size: 7.5px;
$owned-media-service-mobile-progress-line-width: 165px;
$owned-media-service-mobile-progress-line-height: 2px;
$owned-media-service-mobile-progress-line-top: 9px;
$owned-media-service-mobile-progress-line-left: 27.5px;
$owned-media-service-mobile-progress-dot-positions: (20px, 65px, 110px, 155px, 200px);

// Mobile Typography
$owned-media-service-mobile-title-font-size: clamp(32px, 6vw, 40px);
$owned-media-service-mobile-title-letter-spacing: 0.4px;
$owned-media-service-mobile-subtitle-font-size: clamp(18px, 4vw, 20px);
$owned-media-service-mobile-subtitle-letter-spacing: 0.2px;
$owned-media-service-mobile-number-main-font-size: 64px;
$owned-media-service-mobile-number-main-line-height: 1;
$owned-media-service-mobile-number-sub-font-size: clamp(12px, 3vw, 14px);
$owned-media-service-mobile-number-sub-letter-spacing: 0.14px;
$owned-media-service-mobile-item-title-font-size: clamp(20px, 5vw, 24px);
$owned-media-service-mobile-item-title-line-height: 1.5;
$owned-media-service-mobile-title-accent-font-size: clamp(24px, 6vw, 28px);
$owned-media-service-mobile-description-font-size: clamp(14px, 3.5vw, 16px);
$owned-media-service-mobile-description-line-height: 1.8;
$owned-media-service-mobile-description-letter-spacing: 0.16px;

// Mobile Decorative Elements
$owned-media-service-mobile-decoration-width: 10px;
$owned-media-service-mobile-decoration-height: 3px;
$owned-media-service-mobile-decoration-margin: 6px;

// Mobile Icon
$owned-media-service-mobile-icon-max-width: 200px;

// Success セクション専用
$owned-media-success-title-bg-top: 25%;
$owned-media-success-title-bg-width: 110%;
$owned-media-success-title-wrapper-z-index: 1;
$owned-media-success-title-z-index: 2;
$owned-media-success-title-mark-bottom: 70px;
$owned-media-success-title-mark-transform: translate(50%, 50%) rotate(15deg);
$owned-media-success-description-gap: 12px;
$owned-media-success-description-margin-bottom: 100px;
$owned-media-success-marker-padding: 2px 4px;
$owned-media-success-marker-border-radius: 4px;

// Success - Typography
$owned-media-success-title-main-font-size: clamp(100px, 10vw, 115px);
$owned-media-success-title-main-weight: 900;
$owned-media-success-title-main-line-height: 1.2;
$owned-media-success-title-main-letter-spacing: 2px;
$owned-media-success-title-sub-font-size: clamp(32px, 10vw, 52px);
$owned-media-success-title-sub-weight: 700;
$owned-media-success-title-sub-line-height: 1.2;
$owned-media-success-title-sub-letter-spacing: 1px;
$owned-media-success-title-mark-font-size: clamp(48px, 10vw, 72px);
$owned-media-success-title-mark-weight: 700;
$owned-media-success-description-text-font-size: clamp(18px, 10vw, 24px);
$owned-media-success-description-text-weight: 700;
$owned-media-success-description-text-line-height: 1.2;
$owned-media-success-description-strong-font-size: clamp(18px, 10vw, 24px);

// Success - Mobile
$owned-media-success-mobile-title-bg-top: 0;
$owned-media-success-mobile-title-main-font-size: clamp(40px, 8vw, 60px);
$owned-media-success-mobile-title-main-line-height: 1.3;
$owned-media-success-mobile-title-main-letter-spacing: 1px;
$owned-media-success-mobile-title-sub-font-size: clamp(24px, 6vw, 36px);
$owned-media-success-mobile-title-sub-line-height: 1.3;
$owned-media-success-mobile-title-sub-letter-spacing: 0.5px;
$owned-media-success-mobile-title-mark-font-size: clamp(30px, 6vw, 40px);
$owned-media-success-mobile-title-mark-margin-top: 10px;
$owned-media-success-mobile-description-margin-bottom: 32px;
$owned-media-success-mobile-description-text-font-size: clamp(18px, 4vw, 24px);
$owned-media-success-mobile-description-text-line-height: 1.4;
$owned-media-success-mobile-description-strong-font-size: clamp(20px, 5vw, 28px);

// System Support セクション専用
$owned-media-system-support-visual-gap: 12px;
$owned-media-system-support-visual-container-gap: 32px;
$owned-media-system-support-arrow-width: 230px;
$owned-media-system-support-arrow-height: 140px;
$owned-media-system-support-arrow-top: 30%;
$owned-media-system-support-arrow-right: -290px;
$owned-media-system-support-arrow-transform: translateX(-50%);
$owned-media-system-support-message-bubble-top: -35px;
$owned-media-system-support-message-bubble-left: 65%;
$owned-media-system-support-message-bubble-size: 70px;
$owned-media-system-support-message-bubble-transform: rotate(45deg);
$owned-media-system-support-message-accent-highlight-height: 12px;

// System Support - Images
$owned-media-system-support-image-1-width: 382px;
$owned-media-system-support-image-1-height: 376px;
$owned-media-system-support-image-2-width: 233px;
$owned-media-system-support-image-2-height: 279px;
$owned-media-system-support-team-image-width: 1080px;
$owned-media-system-support-team-image-height: 712px;

// System Support - Typography
$owned-media-system-support-text-font-size: 40px;
$owned-media-system-support-text-weight: 700;
$owned-media-system-support-message-accent-font-size: 42px;
$owned-media-system-support-message-accent-weight: 900;
$owned-media-system-support-message-accent-letter-spacing: 0.18px;
$owned-media-system-support-message-accent-stroke: 1px;
$owned-media-system-support-message-text-font-size: 42px;
$owned-media-system-support-message-text-weight: 900;
$owned-media-system-support-message-text-letter-spacing: 0.18px;

// System Support - Mobile
$owned-media-system-support-mobile-visual-gap: 40px;
$owned-media-system-support-mobile-container-gap: 20px;
$owned-media-system-support-mobile-text-bottom: -25px;
$owned-media-system-support-mobile-text-width: 70%;
$owned-media-system-support-mobile-text-height: 50px;
$owned-media-system-support-mobile-text-font-size: clamp(24px, 6vw, 32px);
$owned-media-system-support-mobile-arrow-top: -15px;
$owned-media-system-support-mobile-arrow-width: 100px;
$owned-media-system-support-mobile-arrow-height: 40px;
$owned-media-system-support-mobile-arrow-transform: translateX(-50%) rotate(90deg);
$owned-media-system-support-mobile-image-1-width: clamp(200px, 50vw, 280px);
$owned-media-system-support-mobile-image-1-height: clamp(196px, 49vw, 275px);
$owned-media-system-support-mobile-image-2-width: clamp(120px, 30vw, 180px);
$owned-media-system-support-mobile-image-2-height: clamp(144px, 36vw, 216px);
$owned-media-system-support-mobile-message-accent-font-size: 24px;
$owned-media-system-support-mobile-message-accent-weight: 900;
$owned-media-system-support-mobile-message-text-font-size: 24px;
$owned-media-system-support-mobile-message-text-weight: 500;
$owned-media-system-support-mobile-bubble-size: 40px;
$owned-media-system-support-mobile-bubble-transform: translateX(-50%) rotate(45deg)
  skew(30deg, 30deg);

// FAQ セクション専用（既存値を参照・統合）
// 既存変数はそのまま活用し、不足分のみ追加
$owned-media-faq-text-display: flex;
$owned-media-faq-text-align: flex-start;
$owned-media-faq-icon-display: flex;
$owned-media-faq-icon-align: center;
$owned-media-faq-icon-justify: center;
$owned-media-faq-text-line-height: normal;
$owned-media-faq-answer-line-height: 1.5;

// FAQ - Mobile専用（既存値を参照）
$owned-media-faq-mobile-text-line-height: 1.4;
$owned-media-faq-mobile-answer-line-height: 1.6;
