// ////////////////////////////
// ご支援イメージ
// ////////////////////////////
.owned-media-support {
  @include owned-media-section($owned-media-bg-color);
  @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 750px);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  // カードコンテナ
  &__grid {
    display: grid;
    flex-wrap: wrap;
    grid-template-columns: repeat(2, 1fr);
    gap: $owned-media-spacing-large-plus;
    margin: 0 auto;
  }

  &__item {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: $owned-media-spacing-small-plus;
    align-items: center;
    width: 100%;
    height: auto;
    padding: 18px;
    overflow: hidden;
    background-color: $owned-media-white;
    border-radius: $owned-media-support-card-border-radius;
    box-shadow: 0 0 14px rgb(0 0 0 / 25%);

    // タイトルエリア
    &-title-area {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: $owned-media-support-title-area-height;
      background-color: $owned-media-support-title-bg;
      border-radius: 14px;
    }

    &-title {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 20px;
      font-weight: 700;
      line-height: 1.2;
      color: $owned-media-white;
      text-align: center;
      white-space: nowrap;
    }

    // 画像エリア
    &-images {
      position: relative;
      bottom: 0;
      box-sizing: border-box;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 163px;
      padding: 18px;
      background-color: $owned-media-support-images-bg;
      border-radius: 8px;
    }

    &-image {
      overflow: hidden;
      border-radius: $owned-media-support-image-border-radius;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }

      &--wide {
        width: 100%;
        max-height: 100%;
      }
    }

    &-content {
      display: flex;
      justify-content: center;
      width: 100%;
      text-align: center;
    }

    &-text {
      margin: 0;
      font-family: $owned-media-font-family-noto;
      font-size: 16px;
      color: $owned-media-text-color;
      letter-spacing: 0.5px;
    }

    &-normal {
      font-size: 16px;
      font-weight: 500;
    }

    &-accent {
      font-size: 16px;
      font-weight: 700;
    }
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    padding: $owned-media-section-padding-desktop 0 $owned-media-section-padding-mobile;

    @include owned-media-section-bg-gradient($owned-media-bg-color, $owned-media-mint-light, 450px);

    &::before {
      height: calc(100% - 200px);
    }

    // &__title {
    //   font-size: 48px;
    // }

    // &__subtitle-text {
    //   font-size: 24px;
    // }
    @include section-header;

    &__grid {
      flex-direction: column;
      grid-template-columns: 1fr;
      gap: $owned-media-spacing-xl;
      align-items: center;
    }

    &__item {
      width: 100%;
      height: auto;
      padding-top: 20px;
      border-radius: 20px;

      &-title-area {
        width: 95%;
        height: 92px;
        padding-right: 12px;
        padding-left: 12px;
      }

      &-title {
        max-width: 100%;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0.8px;
        white-space: wrap;
      }

      &-normal {
        font-size: 20px;
      }

      &-accent {
        font-size: 22px;
        font-weight: 700;
      }

      &-images {
        position: relative;
        bottom: auto;
        left: auto;
        width: 100%;
        height: unset;
        min-height: unset;
        border-radius: 0 0 20px 20px;
      }

      &-content {
        position: relative;
        bottom: auto;
        left: auto;
        width: 95%;
      }

      &-text {
        font-size: 16px;
        line-height: 24px;
      }
    }
  }
}
