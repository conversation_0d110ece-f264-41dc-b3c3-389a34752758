// ////////////////////////////
// よくある質問 (FAQ)
// ////////////////////////////

.owned-media-faq {
  @include owned-media-section;

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  .faq {
    &__list {
      display: flex;
      flex-direction: column;
    }

    &__item {
      display: flex;
      flex-direction: column;
      gap: $owned-media-faq-item-gap;
      padding: $owned-media-faq-item-padding 0;
      border-bottom: 1px solid $owned-media-faq-border-color;

      .faq-text {
        &-wrap {
          display: flex;
          gap: $owned-media-faq-text-gap;
          align-items: flex-start;

          &.answer {
            padding-left: $owned-media-faq-answer-text-padding-left;
          }
        }

        &.question {
          @include faq-question-style;
        }

        &.answer {
          @include faq-answer-style;
        }

        &-text {
          &.question {
            @include faq-text-content(
              $owned-media-faq-question-font-size,
              700,
              $owned-media-text-color-sumikuro
            );
          }

          &.answer {
            @include faq-text-content(
              $owned-media-faq-answer-font-size,
              400,
              $owned-media-text-color,
              $owned-media-faq-answer-line-height
            );
          }
        }
      }
    }
  }

  @include owned-media-mobile {
    padding: $owned-media-faq-mobile-padding;

    &__container {
      padding: 0 $owned-media-faq-mobile-container-padding;
    }

    &__header {
      margin-bottom: $owned-media-faq-mobile-header-margin-bottom;
    }

    &__title {
      font-size: clamp(32px, 8vw, 40px);
      letter-spacing: -0.5px;
    }

    &__subtitle {
      font-size: clamp(18px, 4vw, 20px);
      letter-spacing: 0.2px;
    }

    .faq {
      &__item {
        gap: $owned-media-faq-mobile-item-gap;
        padding: $owned-media-faq-mobile-item-padding 0;
        border-bottom: 1px solid $owned-media-faq-border-color;

        .faq-text {
          gap: $owned-media-faq-mobile-text-gap;

          &-wrap {
            gap: $owned-media-faq-mobile-text-gap;

            &.answer {
              padding-left: 0;
            }
          }

          &::before {
            width: $owned-media-faq-mobile-icon-size;
            height: $owned-media-faq-mobile-icon-size;
            font-size: $owned-media-faq-mobile-icon-font-size;
            border-radius: $owned-media-faq-mobile-icon-border-radius;
          }

          &-text {
            &.question {
              @include faq-text-content(
                clamp(18px, 4.5vw, $owned-media-faq-answer-font-size),
                700,
                $owned-media-text-color-sumikuro,
                $owned-media-faq-mobile-text-line-height
              );
            }

            &.answer {
              @include faq-text-content(
                clamp(16px, 4vw, 18px),
                400,
                $owned-media-text-color,
                $owned-media-faq-mobile-answer-line-height
              );
            }
          }
        }
      }
    }
  }
}