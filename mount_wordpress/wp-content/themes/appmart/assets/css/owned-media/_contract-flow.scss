// ////////////////////////////
// フロー
// ////////////////////////////
.owned-media-contract-flow {
  @include owned-media-section($owned-media-bg-color);

  &__container {
    @include owned-media-container;
  }

  @include section-header;

  &__content {
    display: flex;
    flex-direction: column;
    gap: 32px;
    align-items: center;
    width: 100%;
  }

  .flow-step {
    position: relative;
    display: flex;
    flex: 1;
    gap: $owned-media-contract-flow-step-gap;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    min-height: 162px;
    padding: 22px;
    padding-left: 0;
    background-color: $owned-media-white;
    filter: drop-shadow($owned-media-contract-flow-step-shadow);
    border-radius: $owned-media-contract-flow-step-border-radius;

    &::after {
      position: absolute;
      bottom: -14px;
      left: $owned-media-contract-flow-arrow-left;
      width: 28px;
      height: 28px;
      content: '';
      background-color: $owned-media-white;
      transform: rotate(135deg) skew(20deg, 20deg);
    }

    &__number {
      flex-shrink: 0;
      padding: 28px 32px;
      font-family: $owned-media-font-family-yu;
      font-size: $owned-media-contract-flow-number-font-size;
      font-size: 40px;
      font-weight: 700;
      color: $owned-media-white;
      text-align: center;
      white-space: nowrap;
      background-color: $owned-media-contract-flow-number-bg;
      border-radius: 0 $owned-media-contract-flow-number-border-radius
        $owned-media-contract-flow-number-border-radius 0;
    }

    &__description {
      display: flex;
      flex: 1;
      flex-direction: column;
      gap: $owned-media-contract-flow-description-gap;
      color: $owned-media-text-color-sumikuro;

      &-title {
        font-size: 24px;
        font-weight: 700;
        line-height: normal;
        color: $owned-media-text-color-sumikuro;
      }

      &-text {
        font-family: $owned-media-font-family-noto;
        font-size: 16px;
        font-weight: 400;
        line-height: normal;
      }
    }

    &__icon {
      display: flex;
      flex-shrink: 0;
      align-items: center;
      justify-content: center;
      width: auto;
      max-width: 140px;
      height: auto;
      max-height: 100%;

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    padding: $owned-media-contract-flow-mobile-padding 0;

    &__container {
      padding: 0 $owned-media-contract-flow-mobile-container-padding;
    }

    &__header {
      margin-bottom: $owned-media-contract-flow-mobile-header-margin-bottom;
    }

    &__content {
      gap: $owned-media-contract-flow-mobile-content-gap;
    }

    .flow-step {
      flex-direction: column;
      gap: $owned-media-contract-flow-mobile-step-gap;
      align-items: center;
      min-height: auto;
      padding: 35px 20px;
      overflow: hidden;
      border-radius: $owned-media-contract-flow-mobile-step-border-radius;
      box-shadow: $owned-media-contract-flow-mobile-step-shadow;

      // モバイルでは矢印を非表示
      &::after {
        z-index: 1;
      }

      &__number {
        position: absolute;
        top: $owned-media-contract-flow-mobile-number-top;
        left: $owned-media-contract-flow-mobile-number-left;
        width: $owned-media-contract-flow-mobile-number-size;
        height: $owned-media-contract-flow-mobile-number-size;
        padding: $owned-media-contract-flow-mobile-number-padding;
        font-size: clamp(40px, 8vw, $owned-media-contract-flow-mobile-number-font-size);
        border-radius: 50%;

        &-text {
          position: absolute;
          right: $owned-media-contract-flow-mobile-number-text-right;
          bottom: $owned-media-contract-flow-mobile-number-text-bottom;
          font-size: $owned-media-contract-flow-mobile-number-text-font-size;
          color: $owned-media-white;
        }
      }

      &__description {
        gap: $owned-media-contract-flow-mobile-description-gap;
        text-align: center;

        &-title {
          font-size: clamp(
            28px,
            6vw,
            $owned-media-contract-flow-mobile-description-title-font-size
          );
          line-height: $owned-media-contract-flow-mobile-description-title-line-height;
        }

        &-text {
          font-size: 20px;
          line-height: $owned-media-contract-flow-mobile-description-text-line-height;
        }
      }

      &__icon {
        width: $owned-media-contract-flow-mobile-icon-width;
        height: auto;
      }
    }
  }
}
