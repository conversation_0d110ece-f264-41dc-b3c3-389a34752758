// ////////////////////////////
// メリット
// ////////////////////////////
.owned-media-merit {
  @include owned-media-section($owned-media-base-color);

  &__container {
    @include owned-media-container;

    max-width: 1002px;
  }

  // 背景装飾
  &__decorations {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 1;
    width: 100%;
    height: 100%;
    pointer-events: none;
  }

  &__decoration {
    &--1 {
      @include merit-decoration(
        $owned-media-merit-decoration-width,
        $owned-media-merit-decoration-height,
        $owned-media-merit-decoration-1-top,
        $owned-media-merit-decoration-1-left
      );
    }

    &--2 {
      @include merit-decoration(
        $owned-media-merit-decoration-width,
        $owned-media-merit-decoration-height,
        $owned-media-merit-decoration-2-top,
        auto,
        $owned-media-merit-decoration-2-right
      );
    }

    &--3 {
      @include merit-decoration(
        $owned-media-merit-decoration-width,
        $owned-media-merit-decoration-height,
        $owned-media-merit-decoration-3-top,
        $owned-media-merit-decoration-3-left
      );
    }

    &--4 {
      @include merit-decoration(
        $owned-media-merit-decoration-width,
        $owned-media-merit-decoration-height,
        $owned-media-merit-decoration-4-top,
        auto,
        $owned-media-merit-decoration-4-right
      );
    }

    &--5 {
      @include merit-decoration(
        $owned-media-merit-decoration-width,
        $owned-media-merit-decoration-height,
        $owned-media-merit-decoration-5-top,
        $owned-media-merit-decoration-5-left
      );
    }
  }

  // 見出し
  &__header {
    position: relative;
    z-index: 2;
    display: block;
    margin-bottom: $owned-media-merit-header-margin-bottom;
    text-align: center;
  }

  &__brand {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: -12px;

    &-text {
      @include merit-brand-text(
        $owned-media-merit-brand-text-height,
        $owned-media-merit-brand-text-padding,
        $owned-media-merit-brand-text-radius,
        $owned-media-merit-brand-name-font-size
      );
      @include merit-brand-arrow(
        $owned-media-merit-brand-arrow-size,
        $owned-media-merit-brand-arrow-offset
      );
    }

    &-name {
      margin: 0;

      @include merit-noto-text(
        $owned-media-merit-brand-name-font-size,
        700,
        $owned-media-white,
        1.2,
        0.32px
      );
    }

    &-suffix {
      @include merit-noto-text(
        $owned-media-merit-brand-suffix-font-size,
        700,
        $owned-media-white,
        1.2,
        0.08px
      );
    }

    &-service {
      position: absolute;
      top: $owned-media-merit-brand-service-top;
      left: $owned-media-merit-brand-service-left;

      @include merit-noto-text(
        $owned-media-merit-brand-service-font-size,
        700,
        $owned-media-white,
        1.2,
        0.28px
      );

      white-space: nowrap;
    }
  }

  &__catch-container {
    display: inline;
  }

  &__catch {
    &-line1 {
      display: flex;
      align-items: flex-end;
      justify-content: center;
      margin-bottom: $owned-media-spacing-small;
    }

    &-person {
      // PC表示時のスタイル
      display: inline-flex;
      align-items: flex-end;
    }

    &-number {
      // PC表示時のスタイル
      display: inline-flex;
      align-items: flex-end;
    }

    &-text {
      @include merit-noto-text(
        $owned-media-merit-catch-text-font-size,
        700,
        $owned-media-text-color,
        1.2,
        0.55px
      );
    }

    &-accent {
      @include merit-gradient-text(90px, 590);

      position: relative;
      top: 6px;
      letter-spacing: 0;
    }

    &-accent-sub {
      @include merit-gradient-text(60px, 700);
    }

    &-save {
      @include merit-noto-text(
        $owned-media-merit-catch-save-font-size,
        900,
        $owned-media-mint,
        1.2,
        0.83px
      );
    }
  }

  &__title {
    display: flex;
    align-items: flex-end;
    justify-content: center;

    &-number {
      @include merit-gradient-text($owned-media-merit-title-number-font-size, 590);
    }

    &-text {
      display: flex;
      gap: 10px;
      align-items: baseline;
    }

    &-suffix {
      @include merit-noto-text(
        $owned-media-merit-title-suffix-font-size,
        500,
        $owned-media-text-color,
        1.2,
        0.86px
      );
    }

    &-main {
      @include merit-noto-text(
        $owned-media-merit-title-main-font-size,
        500,
        $owned-media-text-color,
        1.2
      );
    }

    &-accent {
      @include merit-noto-text(
        $owned-media-merit-title-accent-font-size,
        700,
        $owned-media-mint,
        1.2,
        $owned-media-merit-title-accent-letter-spacing
      );
    }
  }

  // メリットリスト
  &__list {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 60px;
  }

  &__item {
    position: relative;
    width: 100%;

    &-content {
      @include merit-item-content($owned-media-spacing-medium);

      gap: 60px;
    }

    &-image {
      // @include merit-item-image($owned-media-merit-item-image-size);
      width: 100%;
      max-width: 463px;

      @include merit-item-image-bg(
        -20%,
        $owned-media-merit-image-bg-offset-left,
        $owned-media-merit-image-bg-max-width,
        $owned-media-merit-image-bg-max-height
      );
    }

    &-img {
      position: relative;
      z-index: 3;
      display: inline-block;
      width: 100%;
      height: auto;
      background-color: $owned-media-base-color;
      border-radius: $owned-media-spacing-medium;
      box-shadow: 0 0 $owned-media-spacing-medium $owned-media-merit-shadow;
    }

    &-text-area {
      display: flex;
      flex: 1;
      flex-direction: column;
      justify-content: flex-start;
      max-width: $owned-media-merit-item-text-max-width;
    }

    &-header-container {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 12px;
    }

    &-header {
    }

    &-number {
      position: relative;
      display: flex;
      gap: 8px;
      align-items: center;
      justify-content: flex-start;
      margin-right: 12px;

      &-text {
        @include merit-sf-pro-text(
          $owned-media-merit-number-text-font-size,
          300,
          $owned-media-mint,
          $owned-media-merit-number-text-letter-spacing
        );
      }

      &-icon {
        @include merit-number-icon(
          $owned-media-merit-number-icon-width,
          $owned-media-merit-number-icon-height,
          $owned-media-merit-number-bg-width,
          $owned-media-merit-number-bg-height
        );
      }

      &-bg {
        position: absolute;
        top: 0;
        left: 3px;
        width: $owned-media-merit-number-bg-width;
        height: $owned-media-merit-number-bg-height;
      }

      &-label {
        position: absolute;
        top: $owned-media-merit-number-label-top;
        top: 50%;
        left: 50%;
        width: 100%;
        line-height: 1;
        text-align: center;
        letter-spacing: 0.49px;
        transform: translate(-50%, -50%);

        @include merit-gradient-text($owned-media-merit-number-label-font-size, 500);
      }
    }

    &-text {
      width: 100%;
    }

    &-title {
      // margin: 0 0 $owned-media-spacing-large;

      // @include merit-noto-text(
      //   $owned-media-merit-item-title-font-size,
      //   700,
      //   $owned-media-text-color,
      //   1.5
      // );
      line-height: 1;
      letter-spacing: 0.7px;

      &-main {
        @include merit-noto-text(
          $owned-media-merit-item-title-main-font-size,
          700,
          $owned-media-primary-color,
          1.5
        );
      }

      &-sub {
        @include merit-noto-text(
          $owned-media-merit-item-title-sub-font-size,
          700,
          $owned-media-text-color,
          1.5
        );
      }

      &-accent {
        @include merit-noto-text(
          $owned-media-merit-item-title-accent-font-size,
          900,
          $owned-media-text-color,
          1.5
        );
      }
    }

    &-desc {
      width: 100%;
      max-width: 452px;
      margin: 0;

      @include merit-noto-text(
        $owned-media-merit-item-desc-font-size,
        400,
        $owned-media-text-color,
        $owned-media-merit-item-desc-line-height,
        $owned-media-merit-item-desc-letter-spacing
      );

      font-weight: 500;
      text-shadow: 0 0 9px $owned-media-white;
    }

    // 偶数アイテムは左右反転
    &--02,
    &--04 {
      @include merit-item-reverse;
    }
  }

  // レスポンシブ対応
  @include media-breakpoint-down(md) {
    min-height: auto;
    padding: $owned-media-section-padding-desktop 0 $owned-media-section-padding-mobile;

    &__container {
      width: 100%;
      max-width: 100%;
      padding: 0 $owned-media-spacing-small;
      margin: 0 auto;
    }

    &__decoration {
      display: none;
    }

    &__header {
      display: flex;
      flex-direction: column;
      width: 100%;
      margin-bottom: $owned-media-merit-mobile-header-margin-bottom;
      text-align: center;
    }

    // スマホ版専用要素のスタイル
    &__sp-person {
      width: 100%;
      margin-bottom: 10px;
      text-align: center;
    }

    &__sp-number {
      display: flex;
      gap: 0;
      align-items: flex-end;
      justify-content: center;
      width: 100%;
      margin-top: 10px;
      text-align: center;
    }

    &__brand {
      order: 2;
      width: 100%;
      margin-bottom: 0;

      &-text {
        width: 100%;
        padding: 12px 28px;
        margin: 0 auto;
        line-height: 1;

        @include merit-brand-text(
          68px,
          12px,
          $owned-media-merit-brand-text-radius,
          $owned-media-merit-mobile-brand-text-font-size
        );
        @include merit-brand-arrow(
          $owned-media-merit-mobile-brand-arrow-size,
          $owned-media-merit-mobile-brand-arrow-offset
        );
      }

      &-name {
        @include merit-noto-text(
          $owned-media-merit-mobile-brand-text-font-size,
          700,
          $owned-media-white
        );
      }

      &-suffix {
        @include merit-noto-text(
          $owned-media-merit-mobile-brand-suffix-font-size,
          700,
          $owned-media-white
        );
      }

      &-service {
        position: static;
        margin-top: 5px;

        @include merit-noto-text(
          $owned-media-merit-mobile-brand-service-font-size,
          700,
          $owned-media-white
        );
      }
    }

    &__catch {
      width: 100%;
      margin-bottom: $owned-media-merit-mobile-catch-margin-bottom;

      &-line1 {
        flex-direction: row;
        gap: 0;
        width: 100%;
      }

      &-text {
        @include merit-noto-text(22px, 700, $owned-media-text-color);

        white-space: nowrap;
      }

      &-divider {
        width: $owned-media-merit-mobile-catch-divider-width;
        height: $owned-media-merit-mobile-catch-divider-height;
      }

      &-wo {
        @include merit-noto-text(
          $owned-media-merit-mobile-catch-wo-font-size,
          700,
          $owned-media-text-color
        );
      }

      &-save {
        @include merit-noto-text(
          $owned-media-merit-mobile-catch-save-font-size,
          900,
          $owned-media-mint
        );

        white-space: nowrap;
      }

      &-accent {
        @include merit-gradient-text(108px, 590);

        position: relative;
        bottom: 0;
        line-height: 1;
      }

      &-accent-sub {
        @include merit-gradient-text(42px, 700);

        line-height: 1.2;
      }
    }

    &__title {
      gap: 0;
      width: 100%;

      &-number {
        @include merit-gradient-text($owned-media-merit-mobile-title-number-font-size, 590);
      }

      &-text {
        gap: $owned-media-merit-mobile-title-text-gap;
        justify-content: center;
      }

      &-suffix,
      &-main {
        @include merit-noto-text(
          $owned-media-merit-mobile-title-suffix-font-size,
          500,
          $owned-media-text-color
        );
      }

      &-accent {
        @include merit-noto-text(
          $owned-media-merit-mobile-title-accent-font-size,
          700,
          $owned-media-mint,
          1.2,
          $owned-media-merit-title-accent-letter-spacing
        );
      }
    }

    &__list {
      gap: $owned-media-merit-mobile-list-gap;
      width: 100%;
    }

    &__item {
      width: 100%;

      &-content {
        @include merit-mobile-item-layout;
      }

      &-image {
        flex-shrink: 0;
        width: 100%;
        height: auto;
        padding: $owned-media-merit-mobile-item-image-padding;
        margin: 0 auto;

        &::before {
          top: 55%;
          right: -60px;
          left: unset;
        }
      }

      &-img {
        max-width: 100%;
        height: auto;
      }

      &-text-area {
        width: 100%;
        max-width: none;
        text-align: center;
      }

      &-header-container {
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }

      &-header {
        width: 100%;
        margin-bottom: $owned-media-merit-mobile-item-header-margin-bottom;
      }

      &-number {
        justify-content: center;
        width: 100%;

        &-text {
          @include merit-sf-pro-text(
            80px,
            300,
            $owned-media-mint,
            $owned-media-merit-number-text-letter-spacing
          );
        }

        &-icon {
          @include merit-number-icon(68px, 68px, 68px, 68px);
        }

        &-bg {
          width: $owned-media-merit-mobile-number-bg-width;
          height: $owned-media-merit-mobile-number-bg-height;
        }

        &-label {
          top: calc(50% + 4px);
          left: calc(50% + 6px);
          transform: translate(-50%, -50%);

          @include merit-gradient-text($owned-media-merit-mobile-number-label-font-size, 500);
        }
      }

      &-text {
        width: 100%;
        text-align: center;
      }

      &-title {
        width: 100%;
        text-align: center;

        @include merit-noto-text(
          $owned-media-merit-mobile-item-title-font-size,
          700,
          $owned-media-text-color,
          1.5
        );

        &-main {
          @include merit-noto-text(28px, 700, $owned-media-primary-color, 1.5);
        }

        &-sub {
          @include merit-noto-text(28px, 700, $owned-media-text-color, 1.5);
        }

        &-accent {
          @include merit-noto-text(28px, 900, $owned-media-text-color, 1.5);
        }
      }

      &-desc {
        width: 100%;
        margin: 0 auto;
        text-align: center;

        @include merit-noto-text(
          20px,
          400,
          $owned-media-text-color,
          $owned-media-merit-mobile-item-desc-line-height,
          $owned-media-merit-item-desc-letter-spacing
        );
      }

      // モバイルでは全て同じレイアウト
      &--02,
      &--04 {
        .owned-media-merit__item-content {
          @include merit-mobile-item-layout;
        }

        .owned-media-merit__item-image {
          &::before {
            top: 55%;
            left: -60px;
          }
        }
      }
    }
  }
}
