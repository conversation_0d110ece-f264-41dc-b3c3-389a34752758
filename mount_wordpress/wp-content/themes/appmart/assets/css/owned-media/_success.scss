// 成果の出るオウンドメディア運用セクション
.owned-media-success {
  @include owned-media-section;
  @include success-background-grid;

  padding-top: 60px;
  padding-bottom: 60px;

  &__container {
    @include owned-media-container;

    flex-direction: row;
    gap: 48px;
  }

  // 見出しエリア
  &__header {
    position: relative;
  }

  &__title-wrapper {
    position: relative;
    display: flex;
    flex-direction: column;
    margin-bottom: $owned-media-section-padding-desktop;

    @include success-title-wrapper-bg;
  }

  &__title {
    @include success-title-text(inherit, inherit, inherit, inherit);
  }

  &__title-main {
    display: block;

    @include success-title-text(
      $owned-media-success-title-main-font-size,
      $owned-media-success-title-main-weight,
      $owned-media-success-title-main-line-height,
      $owned-media-primary-color,
      $owned-media-success-title-main-letter-spacing
    );
  }

  &__title-sub {
    position: relative;
    display: block;
    padding-right: 38px;

    @include success-title-text(
      $owned-media-success-title-sub-font-size,
      $owned-media-success-title-sub-weight,
      $owned-media-success-title-sub-line-height,
      $owned-media-text-color,
      $owned-media-success-title-sub-letter-spacing
    );
  }

  &__title-mark {
    @include success-title-mark;
  }

  // 説明テキスト
  &__description {
    display: flex;
    flex-direction: column;
    gap: $owned-media-success-description-gap;
    align-items: center;
    margin-bottom: $owned-media-success-description-margin-bottom;
    text-align: center;
  }

  &__description-text {
    @include success-description-text(
      $owned-media-success-description-text-font-size,
      $owned-media-success-description-text-weight,
      $owned-media-success-description-text-line-height,
      $owned-media-text-color
    );
    @include success-marker;
  }

  &__description-strong {
    font-size: $owned-media-success-description-strong-font-size;
  }

  &__description-break {
    display: none; // PC版では改行しない
  }

  // 概念図エリア
  &__diagram {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    max-width: 354px;
    height: 100%;

    img {
      width: 100%;
      height: auto;
      object-fit: contain;
    }
  }

  // レスポンシブ対応
  @include owned-media-mobile {
    // 基本レイアウト
    padding: $owned-media-spacing-xl $owned-media-spacing-small;

    &__container {
      flex-direction: column;
      gap: 0;
      align-items: center;
      width: 100%;
      padding: 0;
    }

    // ヘッダーエリア
    &__header {
      margin-bottom: 22px;
    }

    // タイトル
    &__title {
      text-align: center;

      &-wrapper {
        &::before {
          top: $owned-media-success-mobile-title-bg-top;
        }
      }
    }

    &__title-main {
      @include success-title-text(
        $owned-media-success-mobile-title-main-font-size,
        $owned-media-success-title-main-weight,
        $owned-media-success-mobile-title-main-line-height,
        $owned-media-primary-color,
        $owned-media-success-mobile-title-main-letter-spacing
      );
    }

    &__title-sub {
      display: inline-block;
      padding-right: 0;

      @include success-title-text(
        $owned-media-success-mobile-title-sub-font-size,
        $owned-media-success-title-sub-weight,
        $owned-media-success-mobile-title-sub-line-height,
        $owned-media-text-color,
        $owned-media-success-mobile-title-sub-letter-spacing
      );
    }

    &__title-mark {
      position: static;
      display: inline-block;
      margin-top: $owned-media-success-mobile-title-mark-margin-top;
      font-size: $owned-media-success-mobile-title-mark-font-size;
      font-weight: $owned-media-success-title-mark-weight;
      color: $owned-media-text-color;
      transform: none;
    }

    // 説明テキスト
    &__description {
      padding: 0;
      margin-bottom: $owned-media-success-mobile-description-margin-bottom;
    }

    &__description-text {
      @include success-description-text(
        $owned-media-success-mobile-description-text-font-size,
        $owned-media-success-description-text-weight,
        $owned-media-success-mobile-description-text-line-height,
        $owned-media-text-color
      );

      white-space: normal;

      @include success-marker-mobile;
    }

    &__description-strong {
      font-size: $owned-media-success-mobile-description-strong-font-size;
    }

    &__description-break {
      display: block; // モバイルでは改行を表示
    }

    // 概念図エリア
    &__diagram {
      width: 100%;
      height: auto;
      padding: 0;

      img {
        width: 100%;
        height: auto;
        object-fit: contain;
      }
    }
  }
}
