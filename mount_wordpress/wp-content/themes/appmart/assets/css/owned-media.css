.owned-media-lp {
  width: 100%;
  overflow-x: hidden;
}
.owned-media-lp * {
  box-sizing: border-box;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}

@media (max-width: 768px) {
  .u-pc-only {
    display: none !important;
  }
}

.u-sp-only {
  display: none !important;
}
@media (max-width: 768px) {
  .u-sp-only {
    display: block !important;
  }
}
@media (max-width: 768px) {
  .u-sp-only.inline {
    display: inline-flex !important;
  }
}

.u-underline {
  position: relative;
  display: inline-block;
}
.u-underline::before {
  position: absolute;
  bottom: -4px;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 14px;
  content: "";
  background-color: #fff54b;
  border-radius: 7px;
}
.u-underline--mint::before {
  bottom: -4px;
  height: 18px;
  background-color: #b1e2d5;
  border-radius: unset;
}

.u-flex {
  display: flex;
}

.u-flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.u-flex-column {
  display: flex;
  flex-direction: column;
}

.u-mt-1 {
  margin-top: 8px;
}

.u-mt-2 {
  margin-top: 16px;
}

.u-mt-3 {
  margin-top: 24px;
}

.u-mt-4 {
  margin-top: 32px;
}

.u-mt-5 {
  margin-top: 40px;
}

.u-mb-1 {
  margin-bottom: 8px;
}

.u-mb-2 {
  margin-bottom: 16px;
}

.u-mb-3 {
  margin-bottom: 24px;
}

.u-mb-4 {
  margin-bottom: 32px;
}

.u-mb-5 {
  margin-bottom: 40px;
}

.u-text-center {
  text-align: center;
}

.u-text-left {
  text-align: left;
}

.u-text-right {
  text-align: right;
}

.u-font-weight-bold {
  font-weight: 700;
}

.u-font-weight-normal {
  font-weight: 400;
}

.c-section-header {
  text-align: center;
  margin-bottom: 80px;
}
@media (max-width: 768px) {
  .c-section-header {
    margin-bottom: 48px;
  }
}
.c-section-header__title {
  font-size: clamp(52px, 10vw, 80px);
  font-weight: 700;
  color: #3ab795;
  margin-bottom: 24px;
}
@media (max-width: 768px) {
  .c-section-header__title {
    font-size: 28px;
    margin-bottom: 12px;
  }
}
.c-section-header__subtitle {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 20px;
  font-size: 38px;
  font-weight: 500;
  color: #3ab795;
  letter-spacing: 0.38px;
}
.c-section-header__subtitle::before, .c-section-header__subtitle::after {
  content: "";
  display: block;
  width: 15px;
  height: 4px;
  background-color: #3ab795;
  border-radius: 2px;
}
@media (max-width: 768px) {
  .c-section-header__subtitle {
    font-size: 24px;
    gap: 8px;
  }
  .c-section-header__subtitle::before, .c-section-header__subtitle::after {
    height: 2px;
  }
}

.c-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 24px 56px;
  font-size: 38px;
  font-weight: 500;
  text-decoration: none;
  border-radius: 160px;
  transition: all 0.3s ease;
  box-shadow: 0 9px 19px rgba(82, 134, 120, 0.13);
}
.c-button:hover {
  transform: translateY(-3px);
  box-shadow: 0 12px 24px rgba(82, 134, 120, 0.2);
}
.c-button--outline {
  background-color: #f9f9f9;
  border: 9px solid #fa6b58;
  color: #fa6b58;
}
.c-button--outline:hover {
  background-color: #fa6b58;
  color: #fff;
}
.c-button--filled {
  background-color: #fa6b58;
  border: 9px solid #fa6b58;
  color: #fff;
}
.c-button--filled:hover {
  background-color: #f9f9f9;
  color: #fa6b58;
}
@media (max-width: 768px) {
  .c-button {
    width: 100%;
    padding: 10px 20px;
    font-size: clamp(12px, 4vw, 22px);
    border-width: 2px;
    border-radius: 90px;
  }
}

.c-card {
  background-color: #fff;
  border-radius: 20px;
  padding: 40px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}
@media (max-width: 768px) {
  .c-card {
    padding: 20px;
    border-radius: 12px;
  }
}
.c-card__title {
  font-size: 24px;
  font-weight: 700;
  margin-bottom: 16px;
  color: #333;
}
@media (max-width: 768px) {
  .c-card__title {
    font-size: 18px;
    margin-bottom: 12px;
  }
}
.c-card__content {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
}
@media (max-width: 768px) {
  .c-card__content {
    font-size: 14px;
  }
}

.owned-media-cta {
  position: relative;
  width: 100%;
  background-color: #f9f9f9;
}
.owned-media-cta__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 221px;
  padding: 35px 0;
}
@media (max-width: 768px) {
  .owned-media-cta__container {
    max-width: 100%;
  }
}
.owned-media-cta__title-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 240px;
  height: 29px;
}
.owned-media-cta__title {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 187.61px;
  height: 29px;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  text-align: center;
  white-space: nowrap;
}
.owned-media-cta__title-prefix {
  margin-right: 10px;
  color: #333;
  letter-spacing: -3%;
}
.owned-media-cta__title-suffix {
  margin-left: 10px;
  color: #333;
  letter-spacing: -3%;
}
.owned-media-cta__title-text {
  color: #333;
  letter-spacing: -3%;
}
.owned-media-cta__title-accent {
  color: #fa6b58;
  letter-spacing: -3%;
}
.owned-media-cta__title-decoration {
  position: absolute;
  top: 4.69px;
  left: 0;
  z-index: -1;
  width: 240px;
  height: 22.3px;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 240 22.3"><line x1="0" y1="11.15" x2="240" y2="11.15" stroke="%23333333" stroke-width="3"/></svg>');
  background-repeat: no-repeat;
  background-position: left center;
  background-size: 240px 22.3px;
}
.owned-media-cta__buttons {
  display: flex;
  gap: 25px;
  margin-top: 30px;
}
.owned-media-cta__button {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 362px;
  height: 92px;
  padding: 0 32px;
  text-decoration: none;
  border-radius: 92px;
  box-shadow: 0 9px 19px rgba(82, 134, 120, 0.13);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}
.owned-media-cta__button:hover {
  box-shadow: 0 12px 24px rgba(82, 134, 120, 0.2);
  transform: translateY(-3px);
}
.owned-media-cta__button--outline {
  position: relative;
  background-color: #f9f9f9;
  border: 3px solid #fa6b58;
  transition: all 0.3s ease;
}
.owned-media-cta__button--outline::after {
  position: absolute;
  top: 50;
  right: 20px;
  width: 8px;
  height: 8px;
  content: "";
  background-color: transparent;
  border-top: 2px solid #fa6b58;
  border-right: 2px solid #fa6b58;
  transform: rotate(45deg);
}
.owned-media-cta__button--outline::after:hover {
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
}
.owned-media-cta__button--outline .owned-media-cta__button-text {
  font-size: 20px;
  font-weight: 500;
  line-height: 1.35;
  color: #fa6b58;
  white-space: nowrap;
  transition: color 0.3s ease;
}
.owned-media-cta__button--outline:hover {
  background-color: #fa6b58;
  border-color: #fa6b58;
}
.owned-media-cta__button--outline:hover .owned-media-cta__button-text {
  color: #fff;
}
.owned-media-cta__button--outline:hover .owned-media-cta__button-arrow path {
  stroke: #fff;
}
.owned-media-cta__button--outline:hover::after {
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
}
.owned-media-cta__button--filled {
  background-color: #fa6b58;
  border: 3px solid #fa6b58;
  transition: all 0.3s ease;
}
.owned-media-cta__button--filled::after {
  position: absolute;
  top: 50;
  right: 20px;
  width: 8px;
  height: 8px;
  content: "";
  background-color: transparent;
  border-top: 2px solid #fff;
  border-right: 2px solid #fff;
  transform: rotate(45deg);
}
.owned-media-cta__button--filled .owned-media-cta__button-text {
  font-size: 20px;
  font-weight: 700;
  line-height: 1.35;
  color: #fff;
  white-space: nowrap;
  transition: color 0.3s ease;
}
.owned-media-cta__button--filled:hover {
  background-color: #f9f9f9;
  border-color: #fa6b58;
}
.owned-media-cta__button--filled:hover .owned-media-cta__button-text {
  color: #fa6b58;
}
.owned-media-cta__button--filled:hover .owned-media-cta__button-arrow path {
  stroke: #fa6b58;
}
.owned-media-cta__button--filled:hover::after {
  border-top: 2px solid #fa6b58;
  border-right: 2px solid #fa6b58;
}
.owned-media-cta__button-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 20px;
  font-weight: 500;
  line-height: 1.35;
  text-align: center;
  letter-spacing: 1%;
}
@media (max-width: 768px) {
  .owned-media-cta {
    padding: 40px 20px;
  }
  .owned-media-cta__title-wrapper {
    width: clamp(200px, 50vw, 240px);
    height: auto;
  }
  .owned-media-cta__title {
    width: auto;
    height: auto;
    font-size: clamp(18px, 5vw, 24px);
    line-height: 1.4;
    white-space: normal;
  }
  .owned-media-cta__title-decoration {
    top: clamp(16px, 4vw, 20px);
    width: clamp(200px, 50vw, 240px);
    height: 18px;
    background-size: clamp(200px, 50vw, 240px) 18px;
  }
  .owned-media-cta__buttons {
    flex-direction: column;
    gap: 20px;
    width: 100%;
    margin-top: 40px;
  }
  .owned-media-cta__button {
    width: 100%;
    max-width: none;
    height: auto;
    padding: 10px 20px;
    border-radius: 90px;
  }
  .owned-media-cta__button--outline, .owned-media-cta__button--filled {
    font-size: 18px;
    border: 2px solid #fa6b58;
  }
  .owned-media-cta__button--outline .owned-media-cta__button-text, .owned-media-cta__button--filled .owned-media-cta__button-text {
    font-size: 18px;
    font-weight: 400;
    line-height: 1.2;
    white-space: normal;
  }
}

.owned-media-fv {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  padding: 0;
  padding-top: 80px;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(227, 243, 255, 0.4) 33.17%, rgba(129, 204, 231, 0.4) 64.42%);
}
@media (max-width: 768px) {
  .owned-media-fv {
    padding: 80px 0 40px;
  }
}
.owned-media-fv::before {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 0;
  width: 100%;
  height: 100%;
  max-height: 770px;
  content: "";
  background-image: url("../images/s-owned-media/fv-main-illustration.png");
  background-repeat: no-repeat;
  background-position: center bottom;
  transform: translateX(-50%);
}
.owned-media-fv__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  display: flex;
  flex-direction: row;
  max-width: 1400px;
  height: auto;
  min-height: 600px;
  padding-top: 32px;
}
@media (max-width: 768px) {
  .owned-media-fv__container {
    max-width: 100%;
  }
}
.owned-media-fv__container .fv-left {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 50%;
  height: fit-content;
  padding-top: 40px;
  transform: rotate(-5deg);
}
.owned-media-fv__container .fv-left__catch {
  position: relative;
}
.owned-media-fv__container .fv-left__catch .catch-copy {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: fit-content;
  margin-bottom: 20px;
}
.owned-media-fv__container .fv-left__catch .catch-copy::before, .owned-media-fv__container .fv-left__catch .catch-copy::after {
  position: absolute;
  top: 50%;
  width: 4px;
  height: 90px;
  content: "";
  background-color: #333;
}
.owned-media-fv__container .fv-left__catch .catch-copy::before {
  left: -64px;
  transform: translateY(-50%) rotate(-20deg);
}
.owned-media-fv__container .fv-left__catch .catch-copy::after {
  right: -64px;
  transform: translateY(-50%) rotate(20deg);
}
.owned-media-fv__container .fv-left__catch .catch-copy__text {
  font-size: clamp(18px, 2.5vw, 24px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  text-align: center;
}
.owned-media-fv__container .fv-left__catch .catch-copy__char-dot {
  position: relative;
  display: inline-block;
}
.owned-media-fv__container .fv-left__catch .catch-copy__char-dot::before {
  position: absolute;
  top: -5px;
  left: 50%;
  width: 5px;
  height: 5px;
  content: "";
  background-color: #333;
  border-radius: 50%;
  transform: translateX(-50%);
}
.owned-media-fv__container .fv-left__pill {
  display: flex;
  align-items: center;
  width: fit-content;
  height: 80px;
  margin-bottom: 24px;
}
.owned-media-fv__container .fv-left__pill .pill {
  padding: 12px 24px;
  font-size: clamp(24px, 3vw, 30px);
  font-weight: 700;
  line-height: 1;
}
.owned-media-fv__container .fv-left__pill .pill-left {
  position: relative;
  z-index: 2;
  color: #fff;
  background-color: #333;
  border: 5px solid #333;
  border-radius: 50px 0 0 50px;
  outline: 1px solid #333;
  outline-offset: -1px;
  margin-right: -2px;
  box-shadow: 1px 0 0 0 #333;
  padding-right: 16px;
  border-right: none;
}
.owned-media-fv__container .fv-left__pill .pill-left__text {
  color: #fff;
}
.owned-media-fv__container .fv-left__pill .pill-right {
  position: relative;
  z-index: 1;
  color: #333;
  background-color: #fff;
  border: 5px solid #333;
  border-radius: 0 50px 50px 0;
  outline: 1px solid #333;
  outline-offset: -1px;
  padding-left: 16px;
  border-left: none;
}
.owned-media-fv__container .fv-left__pill .pill-right__text {
  color: #333;
}
.owned-media-fv__container .fv-left__message {
  display: flex;
  flex-direction: column;
  gap: 16px;
  align-items: center;
  justify-content: center;
}
.owned-media-fv__container .fv-left__message .message-text {
  vertical-align: middle;
}
.owned-media-fv__container .fv-left__message .message-text.strong {
  font-size: clamp(100px, 10vw, 130px);
  font-weight: 900;
  line-height: 1;
  color: #fa6b58;
  white-space: nowrap;
}
.owned-media-fv__container .fv-left__message .message-text.accent {
  font-size: clamp(80px, 8vw, 100px);
  font-weight: 900;
  line-height: 1;
  color: #333;
  white-space: nowrap;
}
.owned-media-fv__container .fv-left__message .message-text.normal {
  font-size: clamp(60px, 7vw, 70px);
  font-weight: 900;
  line-height: 1;
  color: #333;
  white-space: nowrap;
}
.owned-media-fv__container .fv-right {
  position: absolute;
  top: 32px;
  right: 16px;
  display: flex;
  flex: 1;
  justify-content: flex-end;
  width: 50%;
}
.owned-media-fv__container .fv-right__form .form-container {
  width: 375px;
  height: 720px;
  padding: 20px;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 24px rgba(0, 0, 0, 0.25);
}
.owned-media-fv__container .fv-right__form .form-container__text {
  font-size: 24px;
  font-weight: 700;
}
@media (max-width: 1200px) {
  .owned-media-fv {
    position: relative;
    height: 1200px;
  }
  .owned-media-fv::before {
    top: unset;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 530px;
    transform: translateX(30%);
  }
  .owned-media-fv__container {
    flex-direction: column;
    width: 100%;
    height: auto;
    padding: 0 16px;
  }
  .owned-media-fv .fv-left {
    width: 100%;
    height: auto;
    margin-bottom: 40px;
  }
  .owned-media-fv .fv-right {
    position: relative;
    width: 100%;
    margin-bottom: 40px;
  }
  .owned-media-fv .fv-right__form {
    position: absolute;
    top: 0;
    left: 16px;
    display: none;
    display: flex;
    justify-content: flex-start;
    width: 100%;
  }
}
@media (max-width: 768px) {
  .owned-media-fv {
    height: 813px;
    padding-top: 40px;
  }
  .owned-media-fv::before {
    top: unset;
    bottom: 0;
    left: 50%;
    width: 100%;
    background-image: url("../images/s-owned-media/fv-main-illustration-sp.png");
    background-position: center bottom;
    background-size: 100%;
    transform: translateX(-50%);
  }
  .owned-media-fv__container {
    flex-direction: column;
    align-items: center;
    height: auto;
    padding-top: 0;
    padding-right: 0;
    padding-left: 0;
  }
  .owned-media-fv__container .fv-left {
    justify-content: flex-start;
    width: 100%;
    height: 600px;
    padding: 20px;
    transform: none;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy {
    margin-bottom: 20px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::before, .owned-media-fv__container .fv-left__catch .catch-copy::after {
    height: 60px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::before {
    left: -12px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy::after {
    right: -12px;
  }
  .owned-media-fv__container .fv-left__catch .catch-copy__text {
    font-size: clamp(18px, 4vw, 32px);
  }
  .owned-media-fv__container .fv-left__pill {
    display: flex;
    justify-content: center;
    min-width: 100%;
    height: 60px;
    margin-bottom: 20px;
  }
  .owned-media-fv__container .fv-left__pill .pill {
    padding: 12px;
    font-size: clamp(22px, 4vw, 48px);
    border: 2px solid #333;
  }
  .owned-media-fv__container .fv-left__pill .pill-left {
    padding-right: 4px;
  }
  .owned-media-fv__container .fv-left__pill .pill-right {
    padding-left: 4px;
  }
  .owned-media-fv__container .fv-left__message {
    gap: 16px;
  }
  .owned-media-fv__container .fv-left__message .message-text.strong {
    font-size: clamp(80px, 10vw, 90px);
  }
  .owned-media-fv__container .fv-left__message .message-text.accent {
    font-size: clamp(50px, 10vw, 60px);
  }
  .owned-media-fv__container .fv-left__message .message-text.normal {
    font-size: clamp(40px, 10vw, 50px);
  }
  .owned-media-fv__container .fv-right {
    display: none;
  }
  .owned-media-fv__form-mobile {
    display: block;
    width: 100%;
    padding: 40px 20px;
    margin-top: 40px;
  }
  .owned-media-fv__form-mobile .form-container {
    width: 100%;
    max-width: 375px;
    height: auto;
    padding: 20px;
    margin: 0 auto;
  }
  .owned-media-fv__form-mobile .form-container__text {
    font-size: 20px;
  }
}
@media (max-width: 480px) {
  .owned-media-fv {
    padding-top: 0;
  }
}

.owned-media-first-appeal {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #b1e2d5;
  width: 100%;
  padding-top: 22px;
  padding-bottom: 28px;
}
@media (max-width: 768px) {
  .owned-media-first-appeal {
    padding: 80px 0 40px;
  }
}
.owned-media-first-appeal__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-first-appeal__container {
    max-width: 100%;
  }
}
.owned-media-first-appeal__container::after {
  position: absolute;
  top: -60px;
  left: 50%;
  z-index: 1;
  width: 55px;
  height: 55px;
  content: "";
  background-image: url("../images/s-owned-media/first-appeal-decoration.png");
  background-repeat: no-repeat;
  background-size: contain;
  transform: translateX(-50%) rotate(5deg);
}
.owned-media-first-appeal__message-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  text-align: center;
}
.owned-media-first-appeal__main-message {
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 8px;
  align-items: flex-end;
  justify-content: center;
  margin-bottom: 50px;
}
.owned-media-first-appeal__main-message-line1, .owned-media-first-appeal__main-message-line2 {
  display: contents;
}
.owned-media-first-appeal__main-message-from {
  margin-right: 8px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 50px;
  font-weight: 900;
  line-height: 0.86;
  color: #333;
  letter-spacing: -0.03em;
  transform: rotate(-5deg);
}
.owned-media-first-appeal__main-message-connector {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 900;
  line-height: 1;
  color: #333;
  letter-spacing: -0.03em;
}
.owned-media-first-appeal__main-message-to {
  position: relative;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 50px;
  font-weight: 900;
  line-height: 1.5;
  color: #fa6b58;
  letter-spacing: -0.03em;
  transform: rotate(-5deg);
}
.owned-media-first-appeal__main-message-to::after {
  position: absolute;
  bottom: -50px;
  left: 50%;
  z-index: 2;
  width: 179px;
  height: 62px;
  content: "";
  background-image: url("../images/s-owned-media/graffiti-vector.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%) rotate(5deg);
}
.owned-media-first-appeal__main-message-suffix {
  margin-left: -22px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 900;
  line-height: 1.54;
  color: #333;
  letter-spacing: -0.03em;
}
.owned-media-first-appeal__sub-message {
  display: flex;
  flex-direction: column;
  gap: 28px;
  width: 100%;
  text-align: center;
}
.owned-media-first-appeal__sub-message-text {
  display: flex;
  flex-direction: column;
  gap: 4px;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(18px, 2.5vw, 24px);
  font-weight: 700;
  line-height: 1.82;
  text-align: center;
  letter-spacing: -0.03em;
}
@media (max-width: 768px) {
  .owned-media-first-appeal__sub-message-text {
    gap: 8px;
  }
}
.owned-media-first-appeal__sub-message-line1 {
  display: block;
  color: #333;
}
@media (max-width: 768px) {
  .owned-media-first-appeal__sub-message-line1 {
    word-break: keep-all;
    overflow-wrap: break-word;
  }
}
.owned-media-first-appeal__sub-message-line2 {
  display: inline-block;
  width: fit-content;
  height: auto;
  padding: 12px 56px;
  margin: 0 auto;
  font-size: 24px;
  line-height: 1.4;
  color: #fff;
  text-align: center;
  background-color: #fa6b58;
  border-radius: 4px;
}
.owned-media-first-appeal__people {
  display: flex;
  flex-wrap: wrap;
  gap: clamp(20px, 5vw, 20px);
  align-items: center;
  justify-content: center;
  width: 100%;
  margin-bottom: 0;
}
.owned-media-first-appeal__people-message {
  order: 3;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(40px, 5vw, 50px);
  font-weight: 900;
  line-height: 1.4;
  color: #333;
  text-align: center;
  letter-spacing: -2.34px;
  white-space: nowrap;
}
.owned-media-first-appeal__people-images {
  display: contents;
}
.owned-media-first-appeal__person--1 {
  order: 1;
  width: clamp(35px, 8vw, 47px);
  height: 180px;
  object-fit: contain;
  object-position: bottom;
}
.owned-media-first-appeal__person--2 {
  order: 2;
  width: clamp(55px, 12vw, 73px);
  height: 180px;
  object-fit: contain;
  object-position: bottom;
}
.owned-media-first-appeal__person--3 {
  order: 3;
  width: clamp(38px, 9vw, 51px);
  height: 180px;
  object-fit: contain;
  object-position: bottom;
}
.owned-media-first-appeal__person--4 {
  order: 4;
  width: clamp(49px, 11vw, 65px);
  height: 180px;
  object-fit: contain;
  object-position: bottom;
}
.owned-media-first-appeal__decoration {
  position: absolute;
  top: 0;
  right: 5%;
  width: clamp(150px, 15vw, 219px);
  height: auto;
  object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-first-appeal {
    padding: 60px 0 40px;
  }
  .owned-media-first-appeal__container {
    padding: 0 15px;
  }
  .owned-media-first-appeal__container::after {
    top: -120px;
    width: 95px;
    height: 95px;
  }
  .owned-media-first-appeal__main-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 120px;
  }
  .owned-media-first-appeal__main-message::after {
    bottom: -100px;
    left: 50%;
    width: 100%;
    height: 100px;
    transform: translateX(-50%);
  }
  .owned-media-first-appeal__main-message-line1, .owned-media-first-appeal__main-message-line2 {
    display: flex;
    gap: 8px;
    align-items: center;
    justify-content: center;
  }
  .owned-media-first-appeal__main-message-line1 {
    margin-right: 0;
    margin-bottom: 8px;
  }
  .owned-media-first-appeal__main-message-from {
    margin-right: 0;
    font-size: 50px;
    line-height: 1;
    vertical-align: bottom;
    transform: rotate(-5deg);
  }
  .owned-media-first-appeal__main-message-connector {
    font-size: clamp(24px, 6vw, 40px);
  }
  .owned-media-first-appeal__main-message-to {
    font-size: 50px;
    line-height: 1;
    vertical-align: bottom;
    transform: rotate(-5deg);
  }
  .owned-media-first-appeal__main-message-to::after {
    bottom: -70px;
  }
  .owned-media-first-appeal__main-message-suffix {
    position: static;
    left: unset;
    font-size: clamp(24px, 6vw, 40px);
  }
  .owned-media-first-appeal__sub-message {
    gap: 12px;
    margin-bottom: 0;
  }
  .owned-media-first-appeal__sub-message-text {
    font-size: clamp(16px, 4vw, 24px);
    line-height: 1.6;
    text-align: center;
    word-wrap: break-word;
    white-space: normal;
  }
  .owned-media-first-appeal__sub-message-line2 {
    display: inline-block;
    width: 100%;
    height: auto;
    padding: 8px 12px;
    margin: 0;
    font-size: clamp(14px, 4vw, 20px);
  }
  .owned-media-first-appeal__people {
    flex-direction: column;
    gap: 20px;
    align-items: center;
    width: 100%;
    margin-bottom: 20px;
  }
  .owned-media-first-appeal__people-message {
    order: -1;
    margin-bottom: 0;
    font-size: clamp(30px, 10vw, 40px);
  }
  .owned-media-first-appeal__people-images {
    display: flex;
    gap: 10px;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
  }
  .owned-media-first-appeal__person--1, .owned-media-first-appeal__person--2, .owned-media-first-appeal__person--3, .owned-media-first-appeal__person--4 {
    order: unset;
    height: clamp(80px, 20vw, 120px);
  }
  .owned-media-first-appeal__decoration {
    display: none;
  }
}

.owned-media-partner-logos {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  width: 100%;
  padding: 40px 0;
  overflow: hidden;
}
@media (max-width: 768px) {
  .owned-media-partner-logos {
    padding: 80px 0 40px;
  }
}
.owned-media-partner-logos__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  max-width: unset;
  padding: 0;
}
@media (max-width: 768px) {
  .owned-media-partner-logos__container {
    max-width: 100%;
  }
}
.owned-media-partner-logos__logos-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  min-height: 120px;
}
.owned-media-partner-logos__row {
  position: relative;
  display: flex;
  flex-wrap: nowrap;
  gap: 25px;
  align-items: center;
  justify-content: center;
  width: 100%;
  overflow: hidden;
  background-color: #fff;
}
.owned-media-partner-logos__row--1 {
  margin-bottom: 15px;
}
.owned-media-partner-logos__row--2 {
  margin-top: 15px;
}
.owned-media-partner-logos__logo-group {
  object-fit: contain;
  mix-blend-mode: multiply;
  width: auto;
  height: auto;
  max-height: 60px;
  margin: 0 15px;
}
.owned-media-partner-logos__scrolling-container {
  display: none;
}
.owned-media-partner-logos__scrolling-track {
  display: flex;
  gap: 15px;
  align-items: center;
  width: max-content;
}
.owned-media-partner-logos__pc-scrolling-track {
  display: flex;
  gap: 25px;
  align-items: center;
  width: max-content;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}
@media (max-width: 768px) {
  .owned-media-partner-logos {
    height: 200px;
    padding: 40px 0;
    overflow: hidden;
  }
  .owned-media-partner-logos__container {
    position: relative;
    width: 100%;
    max-width: none;
    height: 200px;
    padding: 0;
    overflow: hidden;
  }
  .owned-media-partner-logos__logos-wrapper {
    display: none;
  }
  .owned-media-partner-logos__scrolling-container {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 200px;
  }
  .owned-media-partner-logos__scrolling-row {
    display: flex;
    align-items: center;
    width: 100%;
    height: 50px;
    overflow: hidden;
    background-color: #fff;
  }
  .owned-media-partner-logos__scrolling-row--top {
    margin-bottom: 15px;
  }
  .owned-media-partner-logos__scrolling-row--top .owned-media-partner-logos__scrolling-track {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  .owned-media-partner-logos__scrolling-row--bottom {
    margin-top: 0;
    margin-bottom: 36px;
  }
  .owned-media-partner-logos__scrolling-row--bottom .owned-media-partner-logos__scrolling-track {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }
  .owned-media-partner-logos__scrolling-track {
    display: flex;
    gap: 30px;
    align-items: center;
    width: max-content;
  }
  .owned-media-partner-logos__scrolling-logo {
    object-fit: contain;
    mix-blend-mode: multiply;
    flex-shrink: 0;
    width: auto;
    height: auto;
    max-height: 45px;
    margin: 0 15px;
  }
}
@keyframes scroll-logos {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-1 * var(--track-width)));
  }
}
@keyframes scroll-logos-pc {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(calc(-1 * var(--track-width)));
  }
}

.owned-media-empathy {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  position: relative;
  width: 100%;
}
@media (max-width: 768px) {
  .owned-media-empathy {
    padding: 80px 0 40px;
  }
}
.owned-media-empathy__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}
@media (max-width: 768px) {
  .owned-media-empathy__container {
    max-width: 100%;
  }
}
.owned-media-empathy__header {
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
  justify-content: center;
  margin-bottom: 75px;
}
.owned-media-empathy__subtitle {
  margin: 0 auto;
  text-align: center;
}
.owned-media-empathy__subtitle-text {
  position: relative;
  z-index: 2;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: -3%;
}
.owned-media-empathy__title {
  display: inline-block;
  text-align: center;
}
.owned-media-empathy__title-text {
  position: relative;
  z-index: 2;
  display: block;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 30px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  text-align: center;
  letter-spacing: -3%;
}
@media (max-width: 768px) {
  .owned-media-empathy__title-text {
    font-size: 56px;
  }
}
.owned-media-empathy__title-line1, .owned-media-empathy__title-line2 {
  display: block;
  text-align: center;
}
@media (max-width: 768px) {
  .owned-media-empathy__title-line1, .owned-media-empathy__title-line2 {
    font-size: clamp(28px, 5vw, 40px);
    line-height: 1.4;
  }
}
.owned-media-empathy__checklist {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  width: 100%;
  max-width: 860px;
  padding: 60px;
  border: 8px solid #b1e2d5;
}
.owned-media-empathy__checklist::after {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 60px;
  height: 60px;
  content: "";
  background: linear-gradient(45deg, #b1e2d5 50%, #f9f9f9 50%);
}
.owned-media-empathy__list {
  display: flex;
  flex-direction: column;
  width: 100%;
  list-style: none;
}
.owned-media-empathy__item {
  position: relative;
  display: flex;
  align-items: flex-start;
  padding: 28px 0;
}
.owned-media-empathy__item::before {
  position: absolute;
  bottom: 50%;
  left: 0;
  z-index: 1;
  width: 38px;
  height: 38px;
  content: "";
  border: 7px solid #7dc8b6;
  border-radius: 9px;
  transform: translateY(50%);
}
.owned-media-empathy__item::after {
  position: absolute;
  bottom: 55%;
  left: 8px;
  z-index: 2;
  width: 48px;
  height: 48px;
  content: "";
  background-image: url("../images/s-owned-media/check-icon.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateY(50%);
}
.owned-media-empathy__item-text {
  flex: 1;
  margin: 0;
  margin-left: 96px;
  word-wrap: break-word;
  overflow-wrap: break-word;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 3vw, 24px);
  font-weight: 700;
  line-height: 1;
  color: #333;
}
.owned-media-empathy__item-text::before {
  position: absolute;
  bottom: 0;
  left: 96px;
  width: 90%;
  height: 1px;
  content: "";
  border-bottom: 6px dashed #c9e9ed;
}
.owned-media-empathy__item-accent {
  font-weight: 900;
  color: #fa6b58;
}
@media (max-width: 768px) {
  .owned-media-empathy {
    padding: 40px;
  }
  .owned-media-empathy__container {
    padding: 0;
  }
  .owned-media-empathy__header {
    margin-bottom: 40px;
  }
  .owned-media-empathy__title {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__subtitle {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__subtitle-text {
    font-size: clamp(18px, 5vw, 20px);
    letter-spacing: -0.5px;
  }
  .owned-media-empathy__title-text {
    font-size: clamp(20px, 6vw, 24px);
    letter-spacing: -1px;
    white-space: normal;
  }
  .owned-media-empathy__title-text::after {
    background-color: #b1e2d5;
  }
  .owned-media-empathy__checklist {
    padding: 40px 15px;
    border-width: 4px;
  }
  .owned-media-empathy__checklist::after {
    top: -4px;
    right: -4px;
    width: 31px;
    height: 31px;
  }
  .owned-media-empathy__item {
    padding: 50px 22px;
    padding-bottom: 32px;
    margin-top: 40px;
  }
  .owned-media-empathy__item::before {
    top: -10px;
    bottom: unset;
    left: 50%;
    width: 40px;
    height: 40px;
    border-width: 4px;
    transform: translateX(-50%);
  }
  .owned-media-empathy__item::after {
    top: -20px;
    bottom: unset;
    left: calc(50% + 10px);
    width: 50px;
    height: 50px;
    transform: translateX(-50%);
  }
  .owned-media-empathy__item-text {
    margin-left: 0;
    font-size: 20px;
    line-height: 1.5;
    text-align: center;
  }
  .owned-media-empathy__item-text::before {
    left: 50%;
    border-bottom: 4px dashed #c9e9ed;
    border-bottom-width: 3px;
    transform: translateX(-50%);
  }
}

.owned-media-discovery {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  position: relative;
  width: 100%;
  background-color: #a0c1bb;
}
@media (max-width: 768px) {
  .owned-media-discovery {
    padding: 80px 0 40px;
  }
}
.owned-media-discovery::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  content: "";
  background: linear-gradient(135deg, #a0c1bb 50%, transparent 50%);
}
.owned-media-discovery::after {
  position: absolute;
  top: 0;
  left: 50%;
  z-index: 1;
  width: 60px;
  height: 60px;
  content: "";
  background-color: #f9f9f9;
  transform: translateX(-50%) translateY(-50%) rotate(135deg) skew(20deg, 20deg);
}
.owned-media-discovery__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  width: 100%;
  max-width: 1080px;
  padding-top: 40px;
}
@media (max-width: 768px) {
  .owned-media-discovery__container {
    max-width: 100%;
  }
}
.owned-media-discovery__header {
  width: 100%;
  margin: 0 auto;
}
.owned-media-discovery__title {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
}
.owned-media-discovery__title-prefix {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 5vw, 30px);
  font-weight: 900;
  line-height: 1.2;
  color: #333;
  letter-spacing: -1.5px;
  position: relative;
}
.owned-media-discovery__title-wrapper {
  position: relative;
  display: flex;
}
.owned-media-discovery__title-main {
  position: relative;
  top: -18px;
  margin: 0 15px;
  margin-right: 0;
  white-space: nowrap;
  transform: rotate(-10deg);
  -webkit-text-stroke: 8px #333;
  paint-order: stroke;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(70px, 10vw, 80px);
  font-weight: 900;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 10px;
}
.owned-media-discovery__title-main::before {
  position: absolute;
  top: -30px;
  right: -40px;
  z-index: 1;
  width: 60px;
  height: 66px;
  content: "";
  background-image: url("../images/s-owned-media/discovery-accent.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: rotate(10deg);
}
.owned-media-discovery__title-suffix {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(24px, 5vw, 30px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: -1.83px;
  position: relative;
  margin-left: 15px;
}
.owned-media-discovery__accent {
  position: absolute;
  top: -4px;
  right: 250px;
  width: 101px;
  height: 111px;
}
.owned-media-discovery__content {
  position: relative;
  width: 100%;
  height: 930px;
}
.owned-media-discovery__illustration-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-discovery__illustration {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 10;
  transform: translate(-50%, -50%);
}
.owned-media-discovery__person {
  position: relative;
  top: -200px;
  left: -100px;
  width: auto;
  max-width: 120px;
  height: auto;
  max-height: 255px;
  object-fit: contain;
}
.owned-media-discovery__desk {
  position: absolute;
  top: 0;
  left: -70px;
  width: 230px;
  height: 230px;
  object-fit: contain;
}
.owned-media-discovery__reasons {
  position: relative;
  width: 100%;
  max-width: 1080px;
  height: 100%;
}
.owned-media-discovery__reason {
  position: absolute;
}
.owned-media-discovery__reason--1 {
  top: -30px;
  left: 30px;
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-bubble {
  width: 400px;
  height: 400px;
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-1.png");
  transform: rotate(-15deg);
}
.owned-media-discovery__reason--1 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -80%);
}
.owned-media-discovery__reason--2 {
  top: -28px;
  left: 450px;
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-bubble {
  width: 420px;
  height: 420px;
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-1.png");
  transform: rotate(16deg);
}
.owned-media-discovery__reason--2 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-45%, -80%);
}
.owned-media-discovery__reason--3 {
  top: 230px;
  right: 0px;
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-bubble {
  width: 380px;
  height: 380px;
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-2.png");
}
.owned-media-discovery__reason--3 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -80%);
}
.owned-media-discovery__reason--4 {
  top: 450px;
  left: 500px;
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-bubble {
  width: 380px;
  height: 380px;
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-3.png");
}
.owned-media-discovery__reason--4 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -45%);
}
.owned-media-discovery__reason--5 {
  top: 550px;
  left: 200px;
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-bubble {
  width: 330px;
  height: 330px;
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-4.png");
  transform: rotate(30deg);
}
.owned-media-discovery__reason--5 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -35%);
}
.owned-media-discovery__reason--6 {
  top: 270px;
  left: -50px;
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-bubble {
  width: 380px;
  height: 380px;
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-bubble::before {
  background-image: url("../images/s-owned-media/discovery-bubble-1.png");
  transform: rotate(-160deg);
}
.owned-media-discovery__reason--6 .owned-media-discovery__reason-text {
  top: 50%;
  left: 50%;
  transform: translate(-55%, -35%);
}
.owned-media-discovery__reason-bubble {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: center;
}
.owned-media-discovery__reason-bubble::before {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 0;
  width: 100%;
  height: 100%;
  content: "";
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}
.owned-media-discovery__reason-text {
  position: absolute;
  top: 50%;
  left: 50%;
  z-index: 2;
  width: 80%;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(16px, 2.3vw, 24px);
  line-height: 1.16;
  text-align: center;
  transform: translate(-50%, -50%);
}
.owned-media-discovery__reason-strong {
  font-weight: 900;
  color: #3c8b86;
}
.owned-media-discovery__reason-weak {
  font-weight: 700;
  color: #5f6061;
}
@media (max-width: 768px) {
  .owned-media-discovery {
    height: auto;
    padding: 40px 0;
  }
  .owned-media-discovery__container {
    align-items: center;
    padding: 0 15px;
  }
  .owned-media-discovery__bg-image {
    top: 50px;
    height: auto;
  }
  .owned-media-discovery__arrow {
    top: 20px;
    width: 120px;
    height: 56px;
  }
  .owned-media-discovery__header {
    max-width: 100%;
    padding-top: 80px;
  }
  .owned-media-discovery__title {
    position: relative;
    flex-direction: column;
    gap: 8px;
    align-items: center;
    width: 100%;
  }
  .owned-media-discovery__title-wrapper {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
  .owned-media-discovery__title-prefix {
    display: block;
    font-size: 28px;
  }
  .owned-media-discovery__title-main {
    position: static;
    display: block;
    font-size: 72px;
    letter-spacing: 7.92px;
    -webkit-text-stroke: 7px #333;
    text-stroke: 7px #333;
    transform: rotate(-10deg);
  }
  .owned-media-discovery__title-main::before {
    position: absolute;
    top: 12px;
    right: -32px;
    width: 32px;
    height: 32px;
    content: "";
    background-image: url("../images/s-owned-media/discovery-accent.png");
    background-repeat: no-repeat;
  }
  .owned-media-discovery__title-suffix {
    display: block;
    margin-left: 0;
    font-size: 28px;
    letter-spacing: 1.2px;
  }
  .owned-media-discovery__title-suffix-small {
    display: 24px;
  }
  .owned-media-discovery__accent {
    position: static;
    display: inline-block;
    width: 50px;
    height: 56px;
    margin-left: 10px;
    vertical-align: middle;
  }
  .owned-media-discovery__content {
    position: static;
    max-width: 100%;
    height: auto;
    margin-top: 60px;
  }
  .owned-media-discovery__illustration-container {
    position: static;
    flex-direction: column;
    height: auto;
  }
  .owned-media-discovery__illustration {
    position: static;
    height: 180px;
    margin-bottom: 0;
    text-align: center;
    transform: none;
  }
  .owned-media-discovery__person {
    top: -180px;
    left: -100px;
    width: 120px;
    height: 253px;
  }
  .owned-media-discovery__desk {
    position: absolute;
    top: 250px;
    left: 30%;
    width: 200px;
    height: 198px;
  }
  .owned-media-discovery__reasons {
    position: static;
    display: grid;
    grid-template-columns: 1fr;
    gap: 8px;
    width: 100%;
    padding: 0 10px;
  }
  .owned-media-discovery__reason {
    position: static !important;
  }
  .owned-media-discovery__reason-bubble {
    width: 100% !important;
    height: 94px !important;
    padding: 10px 15px;
    background-color: #e6edf1;
    border-radius: 47px;
    box-shadow: 0 0 10px rgba(135, 159, 170, 0.9);
  }
  .owned-media-discovery__reason-bubble::before {
    display: none;
  }
  .owned-media-discovery__reason-text {
    position: static !important;
    top: auto !important;
    left: auto !important;
    width: 100% !important;
    font-size: 20px;
    transform: none !important;
  }
  .owned-media-discovery__reason-weak {
    font-size: 18px;
  }
}

.owned-media-merit {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
}
@media (max-width: 768px) {
  .owned-media-merit {
    padding: 80px 0 40px;
  }
}
.owned-media-merit__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  max-width: 1002px;
}
@media (max-width: 768px) {
  .owned-media-merit__container {
    max-width: 100%;
  }
}
.owned-media-merit__decorations {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
.owned-media-merit__decoration--1 {
  position: absolute;
  top: 531px;
  width: 213px;
  height: 213px;
  left: 273px;
}
.owned-media-merit__decoration--2 {
  position: absolute;
  top: 1195px;
  width: 213px;
  height: 213px;
  right: 297px;
}
.owned-media-merit__decoration--3 {
  position: absolute;
  top: 1910px;
  width: 213px;
  height: 213px;
  left: 273px;
}
.owned-media-merit__decoration--4 {
  position: absolute;
  top: 2555px;
  width: 213px;
  height: 213px;
  right: 297px;
}
.owned-media-merit__decoration--5 {
  position: absolute;
  top: 3270px;
  width: 213px;
  height: 213px;
  left: 273px;
}
.owned-media-merit__header {
  position: relative;
  z-index: 2;
  display: block;
  margin-bottom: 16px;
  text-align: center;
}
.owned-media-merit__brand {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: -12px;
}
.owned-media-merit__brand-text {
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
  padding: 8px 25px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 0.32px;
  content: "";
  background-color: #3ab795;
  border-radius: 44px;
}
.owned-media-merit__brand-text::before {
  position: absolute;
  bottom: -6px;
  left: 50%;
  z-index: -1;
  display: block;
  width: 18px;
  height: 18px;
  content: "";
  background-color: #3ab795;
  transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
}
.owned-media-merit__brand-name {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 0.32px;
}
.owned-media-merit__brand-suffix {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 0.08px;
}
.owned-media-merit__brand-service {
  position: absolute;
  top: 4px;
  left: 169px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  letter-spacing: 0.28px;
  white-space: nowrap;
}
.owned-media-merit__catch-container {
  display: inline;
}
.owned-media-merit__catch-line1 {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  margin-bottom: 15px;
}
.owned-media-merit__catch-person {
  display: inline-flex;
  align-items: flex-end;
}
.owned-media-merit__catch-number {
  display: inline-flex;
  align-items: flex-end;
}
.owned-media-merit__catch-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 38px;
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  letter-spacing: 0.55px;
}
.owned-media-merit__catch-accent {
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 90px;
  font-weight: 590;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  top: 6px;
  letter-spacing: 0;
}
.owned-media-merit__catch-accent-sub {
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 60px;
  font-weight: 700;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__catch-save {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 83px;
  font-weight: 900;
  line-height: 1.2;
  color: #3ab795;
  letter-spacing: 0.83px;
}
.owned-media-merit__title {
  display: flex;
  align-items: flex-end;
  justify-content: center;
}
.owned-media-merit__title-number {
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 160px;
  font-weight: 590;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__title-text {
  display: flex;
  gap: 10px;
  align-items: baseline;
}
.owned-media-merit__title-suffix {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 86px;
  font-weight: 500;
  line-height: 1.2;
  color: #333;
  letter-spacing: 0.86px;
}
.owned-media-merit__title-main {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 86px;
  font-weight: 500;
  line-height: 1.2;
  color: #333;
}
.owned-media-merit__title-accent {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 106px;
  font-weight: 700;
  line-height: 1.2;
  color: #3ab795;
  letter-spacing: -12px;
}
.owned-media-merit__list {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 60px;
}
.owned-media-merit__item {
  position: relative;
  width: 100%;
}
.owned-media-merit__item-content {
  position: relative;
  display: flex;
  gap: 25px;
  align-items: center;
  justify-content: space-between;
  gap: 60px;
}
.owned-media-merit__item-image {
  width: 100%;
  max-width: 463px;
}
.owned-media-merit__item-image::before {
  position: absolute;
  top: -20%;
  left: -15%;
  z-index: 0;
  width: 100%;
  max-width: 213px;
  height: 100%;
  max-height: 213px;
  content: "";
  background-image: url("../images/s-owned-media/merit-bg.svg");
  background-repeat: no-repeat;
  background-size: 100% 100%;
}
.owned-media-merit__item-img {
  position: relative;
  z-index: 3;
  display: inline-block;
  width: 100%;
  height: auto;
  background-color: #f9f9f9;
  border-radius: 25px;
  box-shadow: 0 0 25px rgba(90, 134, 151, 0.5);
}
.owned-media-merit__item-text-area {
  display: flex;
  flex: 1;
  flex-direction: column;
  justify-content: flex-start;
  max-width: 700px;
}
.owned-media-merit__item-header-container {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
}
.owned-media-merit__item-number {
  position: relative;
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  margin-right: 12px;
}
.owned-media-merit__item-number-text {
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 56px;
  font-weight: 300;
  color: #3ab795;
  letter-spacing: 1.6px;
}
.owned-media-merit__item-number-icon {
  position: relative;
  width: 38px;
  height: 38px;
}
.owned-media-merit__item-number-icon-bg {
  position: absolute;
  top: 0;
  left: 3px;
  width: 38px;
  height: 38px;
}
.owned-media-merit__item-number-icon-label {
  position: absolute;
  top: 12px;
  left: 0;
  width: 100%;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__item-number-bg {
  position: absolute;
  top: 0;
  left: 3px;
  width: 38px;
  height: 38px;
}
.owned-media-merit__item-number-label {
  position: absolute;
  top: 12px;
  top: 50%;
  left: 50%;
  width: 100%;
  line-height: 1;
  text-align: center;
  letter-spacing: 0.49px;
  transform: translate(-50%, -50%);
  font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
  background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
.owned-media-merit__item-text {
  width: 100%;
}
.owned-media-merit__item-title {
  line-height: 1;
  letter-spacing: 0.7px;
}
.owned-media-merit__item-title-main {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.5;
  color: #fa6b58;
}
.owned-media-merit__item-title-sub {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 1.5;
  color: #333;
}
.owned-media-merit__item-title-accent {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 900;
  line-height: 1.5;
  color: #333;
}
.owned-media-merit__item-desc {
  width: 100%;
  max-width: 452px;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.9;
  color: #333;
  letter-spacing: 0.24px;
  font-weight: 500;
  text-shadow: 0 0 9px #fff;
}
.owned-media-merit__item--02 .owned-media-merit__item-content, .owned-media-merit__item--04 .owned-media-merit__item-content {
  flex-direction: row-reverse;
}
.owned-media-merit__item--02 .owned-media-merit__item-image::before, .owned-media-merit__item--04 .owned-media-merit__item-image::before {
  right: -15%;
  left: unset;
}
@media (max-width: 768px) {
  .owned-media-merit {
    min-height: auto;
    padding: 40px 0 40px;
  }
  .owned-media-merit__container {
    width: 100%;
    max-width: 100%;
    padding: 0 15px;
    margin: 0 auto;
  }
  .owned-media-merit__decoration {
    display: none;
  }
  .owned-media-merit__header {
    display: flex;
    flex-direction: column;
    width: 100%;
    margin-bottom: 60px;
    text-align: center;
  }
  .owned-media-merit__sp-person {
    width: 100%;
    margin-bottom: 10px;
    text-align: center;
  }
  .owned-media-merit__sp-number {
    display: flex;
    gap: 0;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    margin-top: 10px;
    text-align: center;
  }
  .owned-media-merit__brand {
    order: 2;
    width: 100%;
    margin-bottom: 0;
  }
  .owned-media-merit__brand-text {
    width: 100%;
    padding: 12px 28px;
    margin: 0 auto;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 68px;
    padding: 12px 25px;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
    letter-spacing: 0.32px;
    content: "";
    background-color: #3ab795;
    border-radius: 44px;
  }
  .owned-media-merit__brand-text::before {
    position: absolute;
    bottom: -12px;
    left: 50%;
    z-index: -1;
    display: block;
    width: 24px;
    height: 24px;
    content: "";
    background-color: #3ab795;
    transform: translateX(-50%) rotate(45deg) skew(15deg, 15deg);
  }
  .owned-media-merit__brand-name {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 20px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
  }
  .owned-media-merit__brand-suffix {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
  }
  .owned-media-merit__brand-service {
    position: static;
    margin-top: 5px;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 18px;
    font-weight: 700;
    line-height: 1.2;
    color: #fff;
  }
  .owned-media-merit__catch {
    width: 100%;
    margin-bottom: 20px;
  }
  .owned-media-merit__catch-line1 {
    flex-direction: row;
    gap: 0;
    width: 100%;
  }
  .owned-media-merit__catch-text {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 22px;
    font-weight: 700;
    line-height: 1.2;
    color: #333;
    white-space: nowrap;
  }
  .owned-media-merit__catch-divider {
    width: 100px;
    height: 3px;
  }
  .owned-media-merit__catch-wo {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(18px, 3vw, 22px);
    font-weight: 700;
    line-height: 1.2;
    color: #333;
  }
  .owned-media-merit__catch-save {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(32px, 4vw, 48px);
    font-weight: 900;
    line-height: 1.2;
    color: #3ab795;
    white-space: nowrap;
  }
  .owned-media-merit__catch-accent {
    font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
    font-size: 108px;
    font-weight: 590;
    text-align: center;
    background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    position: relative;
    bottom: 0;
    line-height: 1;
  }
  .owned-media-merit__catch-accent-sub {
    font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
    font-size: 42px;
    font-weight: 700;
    text-align: center;
    background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
    line-height: 1.2;
  }
  .owned-media-merit__title {
    gap: 0;
    width: 100%;
  }
  .owned-media-merit__title-number {
    font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(60px, 12vw, 80px);
    font-weight: 590;
    text-align: center;
    background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .owned-media-merit__title-text {
    gap: 5px;
    justify-content: center;
  }
  .owned-media-merit__title-suffix, .owned-media-merit__title-main {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(32px, 6vw, 42px);
    font-weight: 500;
    line-height: 1.2;
    color: #333;
  }
  .owned-media-merit__title-accent {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(42px, 8vw, 52px);
    font-weight: 700;
    line-height: 1.2;
    color: #3ab795;
    letter-spacing: -12px;
  }
  .owned-media-merit__list {
    gap: 60px;
    width: 100%;
  }
  .owned-media-merit__item {
    width: 100%;
  }
  .owned-media-merit__item-content {
    flex-direction: column-reverse;
    gap: 30px;
    align-items: center;
    width: 100%;
    min-height: auto;
  }
  .owned-media-merit__item-image {
    flex-shrink: 0;
    width: 100%;
    height: auto;
    padding: 18px;
    margin: 0 auto;
  }
  .owned-media-merit__item-image::before {
    top: 55%;
    right: -60px;
    left: unset;
  }
  .owned-media-merit__item-img {
    max-width: 100%;
    height: auto;
  }
  .owned-media-merit__item-text-area {
    width: 100%;
    max-width: none;
    text-align: center;
  }
  .owned-media-merit__item-header-container {
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .owned-media-merit__item-header {
    width: 100%;
    margin-bottom: 30px;
  }
  .owned-media-merit__item-number {
    justify-content: center;
    width: 100%;
  }
  .owned-media-merit__item-number-text {
    font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
    font-size: 80px;
    font-weight: 300;
    color: #3ab795;
    letter-spacing: 1.6px;
  }
  .owned-media-merit__item-number-icon {
    position: relative;
    width: 68px;
    height: 68px;
  }
  .owned-media-merit__item-number-icon-bg {
    position: absolute;
    top: 0;
    left: 3px;
    width: 68px;
    height: 68px;
  }
  .owned-media-merit__item-number-icon-label {
    position: absolute;
    top: 12px;
    left: 0;
    width: 100%;
    text-align: center;
    background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .owned-media-merit__item-number-bg {
    width: 74px;
    height: 80px;
  }
  .owned-media-merit__item-number-label {
    top: calc(50% + 4px);
    left: calc(50% + 6px);
    transform: translate(-50%, -50%);
    font-family: "SF Pro", "Noto Sans JP", helvetica, sans-serif;
    font-size: 24px;
    font-weight: 500;
    text-align: center;
    background: linear-gradient(164deg, #3ab795 0%, #7fd5bf 100%);
    background-clip: text;
    -webkit-text-fill-color: transparent;
  }
  .owned-media-merit__item-text {
    width: 100%;
    text-align: center;
  }
  .owned-media-merit__item-title {
    width: 100%;
    text-align: center;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(20px, 4vw, 24px);
    font-weight: 700;
    line-height: 1.5;
    color: #333;
  }
  .owned-media-merit__item-title-main {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.5;
    color: #fa6b58;
  }
  .owned-media-merit__item-title-sub {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 28px;
    font-weight: 700;
    line-height: 1.5;
    color: #333;
  }
  .owned-media-merit__item-title-accent {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 28px;
    font-weight: 900;
    line-height: 1.5;
    color: #333;
  }
  .owned-media-merit__item-desc {
    width: 100%;
    margin: 0 auto;
    text-align: center;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 20px;
    font-weight: 400;
    line-height: 1.7;
    color: #333;
    letter-spacing: 0.24px;
  }
  .owned-media-merit__item--02 .owned-media-merit__item-content, .owned-media-merit__item--04 .owned-media-merit__item-content {
    flex-direction: column-reverse;
    gap: 30px;
    align-items: center;
    width: 100%;
    min-height: auto;
  }
  .owned-media-merit__item--02 .owned-media-merit__item-image::before, .owned-media-merit__item--04 .owned-media-merit__item-image::before {
    top: 55%;
    left: -60px;
  }
}

.owned-media-service {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 700px, #b1e2d5 700px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-service {
    padding: 80px 0 40px;
  }
}
.owned-media-service__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-service__container {
    max-width: 100%;
  }
}
.owned-media-service__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-service__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-service__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-service__subtitle::before, .owned-media-service__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-service__subtitle::before {
  margin-right: 8px;
}
.owned-media-service__subtitle::after {
  margin-left: 8px;
}
.owned-media-service__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-service__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-service__title {
    font-size: 28px;
  }
  .owned-media-service__subtitle {
    gap: 4px;
  }
  .owned-media-service__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-service__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-service__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-service__list {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 50px;
  width: 100%;
}
.owned-media-service__item {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 28px;
  padding: 40px 56px;
  background-color: #fff;
  border-radius: 34px;
  box-shadow: 0 0 14px rgba(67, 226, 184, 0.6);
}
.owned-media-service__item:not(:last-child)::after {
  position: absolute;
  bottom: -42px;
  left: 50%;
  width: 140px;
  height: 34px;
  content: "";
  background-image: url("../images/s-owned-media/service-arrow.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.owned-media-service__item-top {
  position: relative;
  display: flex;
  gap: 40px;
  align-items: flex-start;
  padding-right: 200px;
}
.owned-media-service__item-bottom {
  width: 100%;
  padding: 0 50px;
}
.owned-media-service__item-number {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 15px;
  align-items: center;
}
.owned-media-service__item-number::before {
  position: absolute;
  top: -20px;
  left: 50%;
  width: 110px;
  height: 24px;
  content: "";
  background-image: radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), radial-gradient(circle, #d9d9d9 6px, transparent 6px), linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%);
  background-repeat: no-repeat;
  background-position: 0 0, 15px 0, 30px 0, 44px 0, 59px 0, 73px 0, 88px 0, 11px 11px;
  background-size: 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 24px 24px, 88px 3px;
  transform: translateX(-50%);
}
.owned-media-service__item-number-main {
  font-size: clamp(38px, 10vw, 50px);
  font-weight: 500;
  line-height: 1;
  color: #7dc8b6;
  letter-spacing: 1px;
}
.owned-media-service__item-number-sub {
  position: relative;
  font-family: "SF Pro Text", helvetica, sans-serif;
  font-size: 14px;
  font-weight: 500;
  color: #7dc8b6;
  letter-spacing: 0.21px;
}
.owned-media-service__item-number-sub::before, .owned-media-service__item-number-sub::after {
  position: absolute;
  top: 50%;
  display: inline-block;
  width: 10px;
  height: 1px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
  transform: translateY(-50%);
}
.owned-media-service__item-number-sub::before {
  left: -16px;
  margin-right: 8px;
}
.owned-media-service__item-number-sub::after {
  right: -16px;
  margin-left: 8px;
}
.owned-media-service__item-content {
  position: relative;
  flex: 1;
  max-width: 715px;
  min-height: 70px;
}
.owned-media-service__item-content::before {
  position: absolute;
  bottom: -10px;
  left: 0;
  display: block;
  width: 80%;
  height: 5px;
  content: "";
  background-color: #e7eff3;
}
.owned-media-service__item-content::after {
  position: absolute;
  right: 20%;
  bottom: -10px;
  display: block;
  width: 10%;
  height: 5px;
  content: "";
  background-color: #3ab795;
}
.owned-media-service__item-title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(22px, 2.3vw, 28px);
  font-weight: 700;
  position: relative;
  display: inline-block;
  text-align: left;
}
@media (max-width: 768px) {
  .owned-media-service__item-title {
    flex-direction: column;
    text-align: center;
  }
}
.owned-media-service__item-title-line1, .owned-media-service__item-title-line2 {
  display: inline-block;
  text-align: left;
}
@media (max-width: 768px) {
  .owned-media-service__item-title-line1, .owned-media-service__item-title-line2 {
    width: 100%;
  }
}
.owned-media-service__item-title-normal {
  color: #333;
  letter-spacing: 0.2px;
}
.owned-media-service__item-title-accent {
  font-size: clamp(22px, 2.3vw, 28px);
  color: #fa6b58;
  letter-spacing: 0.3px;
}
.owned-media-service__item-description {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #333;
  letter-spacing: 0.5px;
}
.owned-media-service__item-icon {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
}
.owned-media-service__item-icon--01 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 365px);
  height: clamp(200px, 25vw, 250px);
}
.owned-media-service__item-icon--02 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 343px);
  height: clamp(200px, 25vw, 296px);
}
.owned-media-service__item-icon--03 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 385px);
  height: clamp(200px, 25vw, 247px);
}
.owned-media-service__item-icon--04 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 401px);
  height: clamp(200px, 25vw, 296px);
}
.owned-media-service__item-icon--05 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 422px);
  height: clamp(200px, 25vw, 268px);
}
.owned-media-service__item-icon--06 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 439px);
  height: clamp(200px, 25vw, 247px);
}
.owned-media-service__item-icon--07 {
  position: absolute;
  top: -70px;
  right: -5%;
  width: clamp(280px, 25vw, 365px);
  max-width: 240px;
  height: clamp(200px, 25vw, 250px);
  max-height: 150px;
  object-fit: contain;
  width: clamp(280px, 25vw, 498px);
  height: clamp(200px, 25vw, 223px);
}
@media (max-width: 768px) {
  .owned-media-service {
    min-height: auto;
    padding: 40px 15px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 420px, #b1e2d5 420px, #b1e2d5 100%);
  }
  .owned-media-service__container {
    padding: 0;
  }
  .owned-media-service__title {
    font-size: clamp(32px, 6vw, 40px);
    letter-spacing: 0.4px;
  }
  .owned-media-service__subtitle-text {
    font-size: clamp(18px, 4vw, 20px);
    letter-spacing: 0.2px;
  }
  .owned-media-service__list {
    gap: 40px;
    padding: 0;
  }
  .owned-media-service__item {
    gap: 30px;
    padding: 40px 20px;
    border-radius: 20px;
  }
  .owned-media-service__item:not(:last-child)::after {
    display: none;
  }
  .owned-media-service__item-top {
    flex-direction: column;
    gap: 30px;
    align-items: center;
    padding-right: 0;
    margin-right: 0;
  }
  .owned-media-service__item-bottom {
    padding: 0;
    text-align: center;
  }
  .owned-media-service__item-number {
    flex-direction: column;
    gap: 0;
    align-items: center;
    width: 100%;
    padding-top: 30px;
  }
  .owned-media-service__item-number::before {
    position: absolute;
    top: 0;
    left: 50%;
    height: 22px;
    content: "";
    background-image: radial-gradient(circle, #d9d9d9 3.75px, transparent 3.75px), radial-gradient(circle, #d9d9d9 3.75px, transparent 3.75px), radial-gradient(circle, #d9d9d9 3.75px, transparent 3.75px), radial-gradient(circle, #d9d9d9 3.75px, transparent 3.75px), radial-gradient(circle, #d9d9d9 3.75px, transparent 3.75px), linear-gradient(to right, #d9d9d9 0%, #d9d9d9 100%);
    background-repeat: no-repeat;
    background-position: 20px 0, 65px 0, 110px 0, 155px 0, 200px 0, 27.5px 9px;
    background-size: 15px 15px, 15px 15px, 15px 15px, 15px 15px, 15px 15px, 165px 2px;
    transform: translateX(-50%);
  }
  .owned-media-service__item-number-main {
    font-size: 64px;
    line-height: 1;
  }
  .owned-media-service__item-number-sub {
    margin-top: 0;
    font-size: clamp(12px, 3vw, 14px);
    letter-spacing: 0.14px;
  }
  .owned-media-service__item-number-sub::before, .owned-media-service__item-number-sub::after {
    display: inline-block;
    width: 10px;
    height: 3px;
    content: "";
    background-color: #3ab795;
    border-radius: 2px;
  }
  .owned-media-service__item-number-sub::before {
    margin-right: 6px;
  }
  .owned-media-service__item-number-sub::after {
    margin-left: 6px;
  }
  .owned-media-service__item-content {
    flex: none;
    width: 100%;
    max-width: none;
    padding-top: 0;
  }
  .owned-media-service__item-content::before {
    width: 100%;
    height: 7px;
  }
  .owned-media-service__item-content::after {
    right: 0;
    height: 7px;
  }
  .owned-media-service__item-title {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 15px;
    font-size: clamp(20px, 5vw, 24px);
    line-height: 1.5;
  }
  .owned-media-service__item-title-normal {
    text-align: center;
    letter-spacing: 0;
  }
  .owned-media-service__item-title-accent {
    font-size: clamp(24px, 6vw, 28px);
    text-align: center;
    letter-spacing: 0;
  }
  .owned-media-service__item-description {
    font-size: 20px;
    line-height: 1.8;
    letter-spacing: 0.16px;
  }
  .owned-media-service__item-icon {
    position: static;
    width: 100%;
    max-width: 200px;
    height: auto;
    margin: 0 auto;
  }
  .owned-media-service__flow-bg {
    display: none;
  }
}

.owned-media-success {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
  background-image: linear-gradient(to right, rgba(97, 106, 109, 0.15) 1px, transparent 1px), linear-gradient(to bottom, rgba(97, 106, 109, 0.15) 1px, transparent 1px);
  background-size: 60px 60px;
  padding-top: 60px;
  padding-bottom: 60px;
}
@media (max-width: 768px) {
  .owned-media-success {
    padding: 80px 0 40px;
  }
}
.owned-media-success__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
  flex-direction: row;
  gap: 48px;
}
@media (max-width: 768px) {
  .owned-media-success__container {
    max-width: 100%;
  }
}
.owned-media-success__header {
  position: relative;
}
.owned-media-success__title-wrapper {
  position: relative;
  display: flex;
  flex-direction: column;
  margin-bottom: 40px;
}
.owned-media-success__title-wrapper::before {
  position: absolute;
  top: 25%;
  left: 50%;
  z-index: 1;
  width: 110%;
  height: 100%;
  content: "";
  background-image: url("../images/s-owned-media/success-title-bg.png");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  transform: translateX(-50%);
}
.owned-media-success__title {
  position: relative;
  z-index: 2;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: inherit;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  text-align: center;
}
.owned-media-success__title-main {
  display: block;
  position: relative;
  z-index: 2;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(100px, 10vw, 115px);
  font-weight: 900;
  line-height: 1.2;
  color: #fa6b58;
  text-align: center;
  letter-spacing: 2px;
}
.owned-media-success__title-sub {
  position: relative;
  display: block;
  padding-right: 38px;
  position: relative;
  z-index: 2;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(32px, 10vw, 52px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  text-align: center;
  letter-spacing: 1px;
}
.owned-media-success__title-mark {
  position: absolute;
  top: -20px;
  right: 0;
  bottom: 70px;
  font-size: clamp(48px, 10vw, 72px);
  font-weight: 700;
  color: #333;
  transform: translate(50%, 50%) rotate(15deg);
}
.owned-media-success__description {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  margin-bottom: 100px;
  text-align: center;
}
.owned-media-success__description-text {
  position: relative;
  z-index: 1;
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(18px, 10vw, 24px);
  font-weight: 700;
  line-height: 1.2;
  color: #333;
  white-space: nowrap;
}
.owned-media-success__description-text.marker {
  background-color: #fff54b;
}
.owned-media-success__description-strong {
  font-size: clamp(18px, 10vw, 24px);
}
.owned-media-success__description-break {
  display: none;
}
.owned-media-success__diagram {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  max-width: 354px;
  height: 100%;
}
.owned-media-success__diagram img {
  width: 100%;
  height: auto;
  object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-success {
    padding: 40px 15px;
  }
  .owned-media-success__container {
    flex-direction: column;
    gap: 0;
    align-items: center;
    width: 100%;
    padding: 0;
  }
  .owned-media-success__header {
    margin-bottom: 22px;
  }
  .owned-media-success__title {
    text-align: center;
  }
  .owned-media-success__title-wrapper::before {
    top: 0;
  }
  .owned-media-success__title-main {
    position: relative;
    z-index: 2;
    margin: 0;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(40px, 8vw, 60px);
    font-weight: 900;
    line-height: 1.3;
    color: #fa6b58;
    text-align: center;
    letter-spacing: 1px;
  }
  .owned-media-success__title-sub {
    display: inline-block;
    padding-right: 0;
    position: relative;
    z-index: 2;
    margin: 0;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(24px, 6vw, 36px);
    font-weight: 700;
    line-height: 1.3;
    color: #333;
    text-align: center;
    letter-spacing: 0.5px;
  }
  .owned-media-success__title-mark {
    position: static;
    display: inline-block;
    margin-top: 10px;
    font-size: clamp(30px, 6vw, 40px);
    font-weight: 700;
    color: #333;
    transform: none;
  }
  .owned-media-success__description {
    padding: 0;
    margin-bottom: 32px;
  }
  .owned-media-success__description-text {
    position: relative;
    z-index: 1;
    margin: 0;
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(18px, 4vw, 24px);
    font-weight: 700;
    line-height: 1.4;
    color: #333;
    white-space: nowrap;
    white-space: normal;
  }
  .owned-media-success__description-text.marker {
    padding: 2px 4px;
    background-color: #fff54b;
    border-radius: 4px;
  }
  .owned-media-success__description-strong {
    font-size: clamp(20px, 5vw, 28px);
  }
  .owned-media-success__description-break {
    display: block;
  }
  .owned-media-success__diagram {
    width: 100%;
    height: auto;
    padding: 0;
  }
  .owned-media-success__diagram img {
    width: 100%;
    height: auto;
    object-fit: contain;
  }
}

.owned-media-support {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 750px, #b1e2d5 750px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-support {
    padding: 80px 0 40px;
  }
}
.owned-media-support__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-support__container {
    max-width: 100%;
  }
}
.owned-media-support__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-support__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-support__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-support__subtitle::before, .owned-media-support__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-support__subtitle::before {
  margin-right: 8px;
}
.owned-media-support__subtitle::after {
  margin-left: 8px;
}
.owned-media-support__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-support__title {
    font-size: 28px;
  }
  .owned-media-support__subtitle {
    gap: 4px;
  }
  .owned-media-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-support__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-support__grid {
  display: grid;
  flex-wrap: wrap;
  grid-template-columns: repeat(2, 1fr);
  gap: 38px;
  margin: 0 auto;
}
.owned-media-support__item {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 18px;
  align-items: center;
  width: 100%;
  height: auto;
  padding: 18px;
  overflow: hidden;
  background-color: #fff;
  border-radius: 20px;
  box-shadow: 0 0 14px rgba(0, 0, 0, 0.25);
}
.owned-media-support__item-title-area {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 67px;
  background-color: #5f6061;
  border-radius: 14px;
}
.owned-media-support__item-title {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 20px;
  font-weight: 700;
  line-height: 1.2;
  color: #fff;
  text-align: center;
  white-space: nowrap;
}
.owned-media-support__item-images {
  position: relative;
  bottom: 0;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 163px;
  padding: 18px;
  background-color: #e1ede8;
  border-radius: 8px;
}
.owned-media-support__item-image {
  overflow: hidden;
  border-radius: 14px;
}
.owned-media-support__item-image img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
.owned-media-support__item-image--wide {
  width: 100%;
  max-height: 100%;
}
.owned-media-support__item-content {
  display: flex;
  justify-content: center;
  width: 100%;
  text-align: center;
}
.owned-media-support__item-text {
  margin: 0;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 16px;
  color: #333;
  letter-spacing: 0.5px;
}
.owned-media-support__item-normal {
  font-size: 16px;
  font-weight: 500;
}
.owned-media-support__item-accent {
  font-size: 16px;
  font-weight: 700;
}
@media (max-width: 768px) {
  .owned-media-support {
    padding: 40px 0 40px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 450px, #b1e2d5 450px, #b1e2d5 100%);
  }
  .owned-media-support::before {
    height: calc(100% - 200px);
  }
  .owned-media-support__header {
    position: relative;
    display: flex;
    flex-direction: column;
    gap: 24px;
    align-items: center;
    margin-bottom: 80px;
    text-align: center;
  }
  .owned-media-support__title {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: clamp(52px, 10vw, 60px);
    font-weight: 700;
    color: #3ab795;
  }
  .owned-media-support__subtitle {
    position: relative;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: center;
  }
  .owned-media-support__subtitle::before, .owned-media-support__subtitle::after {
    display: inline-block;
    width: 15px;
    height: 4px;
    content: "";
    background-color: #3ab795;
    border-radius: 2px;
  }
  .owned-media-support__subtitle::before {
    margin-right: 8px;
  }
  .owned-media-support__subtitle::after {
    margin-left: 8px;
  }
  .owned-media-support__subtitle-text {
    font-family: "Noto Sans JP", helvetica, sans-serif;
    font-size: 28px;
    font-weight: 500;
    line-height: 1;
    color: #3ab795;
    letter-spacing: 0.38px;
  }
}
@media (max-width: 768px) and (max-width: 768px) {
  .owned-media-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-support__title {
    font-size: 28px;
  }
  .owned-media-support__subtitle {
    gap: 4px;
  }
  .owned-media-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-support__subtitle-text {
    font-size: 24px;
  }
}
@media (max-width: 768px) {
  .owned-media-support__grid {
    flex-direction: column;
    grid-template-columns: 1fr;
    gap: 40px;
    align-items: center;
  }
  .owned-media-support__item {
    width: 100%;
    height: auto;
    padding-top: 20px;
    border-radius: 20px;
  }
  .owned-media-support__item-title-area {
    width: 95%;
    height: 92px;
    padding-right: 12px;
    padding-left: 12px;
  }
  .owned-media-support__item-title {
    max-width: 100%;
    font-size: 20px;
    line-height: 30px;
    letter-spacing: 0.8px;
    white-space: wrap;
  }
  .owned-media-support__item-normal {
    font-size: 20px;
  }
  .owned-media-support__item-accent {
    font-size: 22px;
    font-weight: 700;
  }
  .owned-media-support__item-images {
    position: relative;
    bottom: auto;
    left: auto;
    width: 100%;
    height: unset;
    min-height: unset;
    border-radius: 0 0 20px 20px;
  }
  .owned-media-support__item-content {
    position: relative;
    bottom: auto;
    left: auto;
    width: 95%;
  }
  .owned-media-support__item-text {
    font-size: 16px;
    line-height: 24px;
  }
}

.owned-media-system-support {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 550px, #b1e2d5 550px, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-system-support {
    padding: 80px 0 40px;
  }
}
.owned-media-system-support__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-system-support__container {
    max-width: 100%;
  }
}
.owned-media-system-support__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-system-support__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-system-support__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-system-support__subtitle::before, .owned-media-system-support__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-system-support__subtitle::before {
  margin-right: 8px;
}
.owned-media-system-support__subtitle::after {
  margin-left: 8px;
}
.owned-media-system-support__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-system-support__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-system-support__title {
    font-size: 28px;
  }
  .owned-media-system-support__subtitle {
    gap: 4px;
  }
  .owned-media-system-support__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-system-support__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-system-support__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-system-support__visual {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-system-support__visual .visual-container {
  display: flex;
  align-items: stretch;
  justify-content: space-between;
  width: 100%;
  height: 100%;
}
.owned-media-system-support__visual .visual-left {
  display: flex;
  flex-direction: column;
  gap: 50px;
  align-items: center;
  justify-content: space-between;
  width: 45%;
}
.owned-media-system-support__visual .visual-left__text {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: auto;
  padding: 22px 56px;
  font-size: 40px;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  background-color: #7dc8b6;
  border-radius: 48px;
}
.owned-media-system-support__visual .visual-left__image {
  position: relative;
  flex: 1;
  gap: 20px;
  padding-right: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  padding: 38px 60px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
}
.owned-media-system-support__visual .visual-left__image::after {
  position: absolute;
  top: 35%;
  right: -42%;
  width: 230px;
  height: 110px;
  content: "";
  background-image: url("../images/s-owned-media/support-arrow.png");
  background-repeat: no-repeat;
  background-size: contain;
}
.owned-media-system-support__visual .visual-left__image img {
  flex-shrink: 0;
  object-fit: cover;
}
.owned-media-system-support__visual .visual-left__image img:first-child {
  width: 100%;
  max-width: 245px;
  height: auto;
}
.owned-media-system-support__visual .visual-left__image img:last-child {
  width: 100%;
  max-width: 166px;
  height: auto;
}
.owned-media-system-support__visual .visual-right {
  display: flex;
  flex-direction: column;
  gap: 50px;
  align-items: center;
  justify-content: space-between;
  width: 48%;
  min-height: 300px;
}
.owned-media-system-support__visual .visual-right__text {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: auto;
  padding: 22px 56px;
  font-size: 40px;
  font-weight: 700;
  line-height: 1;
  color: #fff;
  background-color: #7dc8b6;
  border-radius: 48px;
}
.owned-media-system-support__visual .visual-right__image {
  display: flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  height: fit-content;
  padding: 38px 60px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
  flex: 1;
  width: 100%;
}
.owned-media-system-support__visual .visual-right__image img {
  flex-shrink: 0;
  width: 100%;
  max-width: 184px;
  height: auto;
  object-fit: cover;
}
.owned-media-system-support__team-image {
  width: 1080px;
  height: 712px;
  object-fit: contain;
}
.owned-media-system-support__message {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
  margin-top: 22px;
}
.owned-media-system-support__message-bubble {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 116px;
  padding: 22px 56px;
  background-color: #fff;
  border-radius: 18px;
  box-shadow: 0 0 35px 0 rgba(58, 183, 149, 0.6);
}
.owned-media-system-support__message-bubble::before {
  position: absolute;
  top: -20px;
  left: 46.5%;
  z-index: 0;
  width: 40px;
  height: 40px;
  content: "";
  background-color: #fff;
  transform: rotate(45deg) skew(20deg, 20deg);
}
.owned-media-system-support__message-accent {
  position: relative;
  z-index: 2;
  font-size: 28px;
  font-weight: 900;
  color: #fa6b58;
  letter-spacing: 0.18px;
  -webkit-text-stroke: 1px #fa6b58;
}
.owned-media-system-support__message-accent::before {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: -1;
  width: 100%;
  height: 12px;
  content: "";
  background-color: #fff54b;
}
.owned-media-system-support__message-text {
  font-size: 28px;
  font-weight: 900;
  color: #333;
  letter-spacing: 0.18px;
}
@media (max-width: 768px) {
  .owned-media-system-support {
    padding: 40px 0 40px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 400px, #b1e2d5 400px, #b1e2d5 100%);
  }
  .owned-media-system-support__container {
    padding: 0 20px;
  }
  .owned-media-system-support__header {
    margin-bottom: 40px;
  }
  .owned-media-system-support__visual {
    gap: 40px;
  }
  .owned-media-system-support__visual .visual-container {
    flex-direction: column-reverse;
    gap: 20px;
    align-items: center;
  }
  .owned-media-system-support__visual .visual-left {
    position: relative;
    gap: 0;
    width: 100%;
    min-height: auto;
  }
  .owned-media-system-support__visual .visual-left__text {
    position: absolute;
    bottom: -25px;
    left: 50%;
    z-index: 2;
    width: 70%;
    height: 50px;
    padding: 20px 30px;
    font-size: clamp(24px, 6vw, 32px);
    font-weight: 700;
    color: #fff;
    background-color: #7dc8b6;
    border-radius: 30px;
    transform: translateX(-50%);
  }
  .owned-media-system-support__visual .visual-left__image {
    flex-direction: column-reverse;
    gap: 20px;
    width: 100%;
    padding: 40px 20px;
    border-radius: 12px;
  }
  .owned-media-system-support__visual .visual-left__image::after {
    top: -15px;
    left: 50%;
    z-index: 1;
    width: 100px;
    height: 40px;
    content: "";
    transform: translateX(-50%) rotate(90deg);
  }
  .owned-media-system-support__visual .visual-left__image img:first-child {
    width: 100%;
    max-width: 295px;
    height: auto;
    object-fit: contain;
  }
  .owned-media-system-support__visual .visual-left__image img:last-child {
    width: 100%;
    max-width: 236px;
    height: auto;
    object-fit: contain;
  }
  .owned-media-system-support__visual .visual-right {
    position: relative;
    gap: 0;
    width: 100%;
    min-height: auto;
  }
  .owned-media-system-support__visual .visual-right__text {
    position: absolute;
    top: -25px;
    left: 50%;
    z-index: 2;
    width: 70%;
    height: 50px;
    padding: 20px 30px;
    font-size: clamp(24px, 6vw, 32px);
    border-radius: 30px;
    transform: translateX(-50%);
  }
  .owned-media-system-support__visual .visual-right__image {
    width: 100%;
    padding: 40px 20px;
    border-radius: 12px;
    box-shadow: 0 0 8px 0 rgba(58, 183, 149, 0.3);
  }
  .owned-media-system-support__visual .visual-right__image img {
    width: 100%;
    max-width: 236px;
    height: auto;
    object-fit: contain;
  }
  .owned-media-system-support__team-image {
    width: 100%;
    max-width: 100%;
    height: auto;
    margin: 40px 0;
  }
  .owned-media-system-support__message {
    margin-top: 40px;
  }
  .owned-media-system-support__message-bubble {
    z-index: 3;
    flex-direction: column;
    width: 100%;
    height: auto;
    padding: 22px 16px;
    border-radius: 16px;
  }
  .owned-media-system-support__message-bubble::before {
    top: 0;
    left: 60%;
    width: 40px;
    height: 40px;
    transform: translateX(-50%) rotate(45deg) skew(30deg, 30deg);
  }
  .owned-media-system-support__message-accent {
    z-index: 3;
    font-size: 24px;
    font-weight: 900;
    -webkit-text-stroke: unset;
    background-color: #fff54b;
  }
  .owned-media-system-support__message-accent::before {
    display: none;
  }
  .owned-media-system-support__message-text {
    z-index: 3;
    font-size: 24px;
    font-weight: 500;
  }
}

.owned-media-case-study {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
  background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 60%, #b1e2d5 60%, #b1e2d5 100%);
}
@media (max-width: 768px) {
  .owned-media-case-study {
    padding: 80px 0 40px;
  }
}
.owned-media-case-study__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-case-study__container {
    max-width: 100%;
  }
}
.owned-media-case-study__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-case-study__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-case-study__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-case-study__subtitle::before, .owned-media-case-study__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-case-study__subtitle::before {
  margin-right: 8px;
}
.owned-media-case-study__subtitle::after {
  margin-left: 8px;
}
.owned-media-case-study__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-case-study__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-case-study__title {
    font-size: 28px;
  }
  .owned-media-case-study__subtitle {
    gap: 4px;
  }
  .owned-media-case-study__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-case-study__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-case-study__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-case-study__content {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
}
.owned-media-case-study__service-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.owned-media-case-study__service-title {
  position: relative;
  display: flex;
  gap: 20px;
  align-items: center;
  justify-content: center;
  width: 80%;
  height: 67px;
  padding: 20px 48px;
  margin: 0 auto 22px;
  background-color: #fa6b58;
}
.owned-media-case-study__service-category, .owned-media-case-study__service-name {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 700;
  line-height: 28px;
  color: #fff;
  letter-spacing: 0.45px;
  white-space: nowrap;
}
.owned-media-case-study__service-description {
  margin-bottom: 12px;
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 24px;
  font-weight: 500;
  line-height: 28px;
  color: #191919;
  text-align: center;
  letter-spacing: 0.42px;
}
.owned-media-case-study__service-period {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 16px;
  font-weight: 500;
  line-height: 28px;
  color: #989898;
  text-align: center;
  letter-spacing: 0.24px;
}
.owned-media-case-study__results-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding: 40px;
  margin-top: 48px;
  background-color: #fff;
  border: 6px solid #3c8b86;
  border-radius: 20px;
  box-shadow: 0 0 35px 0 rgb(60, 139, 134);
}
.owned-media-case-study__results-container .result-wrapper {
  width: 100%;
}
.owned-media-case-study__results-container .result-wrapper .result-inner {
  display: flex;
  align-items: flex-end;
  justify-content: center;
  width: 100%;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__main {
  font-size: 24px;
  font-weight: 500;
  color: #191919;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__accent {
  font-size: 24px;
  font-weight: 900;
  color: #191919;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-wrapper .result-inner__strong {
  font-size: 50px;
  font-weight: 900;
  color: #fa6b58;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description {
  display: flex;
}
.owned-media-case-study__results-container .result-inner__description .result-container {
  position: absolute;
  top: 500px;
  left: 200px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  width: fit-content;
  padding: 8px 12px;
  border: 2px solid #3ab795;
  border-radius: 20px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-top {
  position: relative;
  z-index: 5;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: fit-content;
  height: 100%;
  padding-bottom: 6px;
  margin-bottom: 16px;
  border-bottom: 3px solid #3ab795;
}
.owned-media-case-study__results-container .result-inner__description .result-container-top::after {
  position: absolute;
  bottom: -16px;
  left: 50%;
  z-index: -1;
  width: 12px;
  height: 12px;
  content: "";
  background-color: #fff;
  border-bottom: 3px solid #3ab795;
  transform: translateY(-50%) rotate(45deg);
}
.owned-media-case-study__results-container .result-inner__description .result-container-top__text {
  font-size: 28px;
  font-weight: 700;
  color: #3ab795;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description .result-container-top__text-main {
  font-size: 20px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom {
  position: relative;
  z-index: 1;
  display: flex;
  flex-flow: row nowrap;
  align-items: flex-end;
  width: 100%;
}
@media (max-width: 768px) {
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom {
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
  }
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__line {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: baseline;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text {
  display: flex;
  align-items: flex-end;
  height: 32px;
  font-weight: 700;
  color: #3ab795;
  white-space: nowrap;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.normal {
  font-size: 20px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.small {
  font-size: 19px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.strong {
  font-size: 40px;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design {
  position: relative;
  top: -28px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 34px;
  height: 34px;
  padding: 0;
  margin: 0 -12px;
  background-color: #3ab795;
  border-radius: 50%;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design::before {
  position: absolute;
  bottom: 0;
  left: 50%;
  z-index: -1;
  width: 12px;
  height: 12px;
  content: "";
  background-color: #3ab795;
  transform: translateX(-50%) rotate(45deg) skew(20deg, 20deg);
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design > .text-md {
  font-size: 10px;
  color: #fff;
}
.owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design > .text-sm {
  font-size: 24px;
  color: #fff;
}
.owned-media-case-study__results-container .result-graph {
  width: 100%;
}
.owned-media-case-study__results-container .result-graph img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-case-study {
    padding: 80px 0 240px;
    background: linear-gradient(to bottom, #f9f9f9 0, #f9f9f9 530px, #b1e2d5 530px, #b1e2d5 100%);
  }
  .owned-media-case-study__container {
    padding: 0 40px 20px;
  }
  .owned-media-case-study__header {
    margin-bottom: 60px;
  }
  .owned-media-case-study__service-title {
    flex-direction: row;
    gap: 0;
    width: 100%;
    height: auto;
    padding: 22px;
    margin: 0 auto 40px;
  }
  .owned-media-case-study__service-category, .owned-media-case-study__service-name {
    font-size: 22px;
    line-height: 1.2;
    white-space: nowrap;
  }
  .owned-media-case-study__service-description {
    margin-bottom: 20px;
    font-size: clamp(20px, 5vw, 28px);
    line-height: 1.4;
  }
  .owned-media-case-study__service-period {
    font-size: clamp(16px, 4vw, 20px);
    line-height: 1.4;
  }
  .owned-media-case-study__content {
    align-items: center;
  }
  .owned-media-case-study__results-container {
    position: relative;
    max-width: 336px;
    height: 439px;
    padding: 40px 20px;
    margin-top: 220px;
    border: 4px solid #3ab795;
    border-width: 4px;
    border-radius: 15px;
    box-shadow: 0 0 4px 0 rgba(60, 139, 134, 0.2);
  }
  .owned-media-case-study__results-container .result-wrapper {
    position: absolute;
    top: -190px;
    left: 0;
    z-index: 3;
    width: 100%;
    padding: 20px;
    background-color: #fff;
    border: 2px solid #b1e2d5;
    border-radius: 15px;
    box-shadow: 0 0 8px 0 rgba(60, 139, 134, 0.2);
  }
  .owned-media-case-study__results-container .result-wrapper::after {
    position: absolute;
    bottom: -20px;
    left: 30%;
    z-index: 0;
    width: 20px;
    height: 20px;
    content: "";
    background-color: #fff;
    transform: translateY(-50%) rotate(45deg) skew(30deg, 30deg);
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-end;
    justify-content: center;
    line-height: 1.2;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__main, .owned-media-case-study__results-container .result-wrapper .result-inner__accent {
    font-size: 24px;
    text-align: center;
    white-space: nowrap;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__strong {
    font-size: 32px;
    text-align: center;
    white-space: nowrap;
  }
  .owned-media-case-study__results-container .result-wrapper .result-inner__break {
    flex-basis: 100%;
    height: 0;
  }
  .owned-media-case-study__results-container .result-inner__description {
    width: 100%;
    height: 180px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container {
    position: absolute;
    top: 430px;
    left: 0;
    z-index: 3;
    align-items: center;
    justify-content: flex-start;
    width: 100%;
    height: 200px;
    height: auto;
    padding: 20px;
    margin-top: 22px;
    background-color: #fff;
    border-width: 2px;
    border-radius: 15px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container::before {
    position: absolute;
    top: -12px;
    left: 30%;
    z-index: 5;
    width: 24px;
    height: 24px;
    content: "";
    background-color: #fff;
    border-top: 1px solid #3ab795;
    border-left: 1px solid #3ab795;
    transform: translateX(-50%) rotate(45deg) skew(20deg, 20deg);
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-top {
    align-items: flex-start;
    height: unset;
    padding-bottom: 8px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-top__text {
    font-size: 24px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom {
    display: flex;
    flex-direction: column;
    gap: 8px;
    align-items: flex-start;
    height: auto;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__line {
    display: flex;
    align-items: flex-end;
    justify-content: center;
    width: 100%;
    line-height: 1;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text {
    display: inline-flex;
    align-items: flex-end;
    line-height: 1;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.normal {
    font-size: 20px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.small {
    font-size: 16px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.strong {
    font-size: 32px;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design {
    position: unset;
    top: unset;
    bottom: unset;
    left: unset;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design .text-lg {
    font-size: 28px;
    color: #fff;
  }
  .owned-media-case-study__results-container .result-inner__description .result-container-bottom__text.design .text-md {
    font-size: 20px;
    color: #fff;
  }
  .owned-media-case-study__results-container .result-graph {
    margin-top: 0;
  }
}

.owned-media-contract-flow {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #f9f9f9;
}
@media (max-width: 768px) {
  .owned-media-contract-flow {
    padding: 80px 0 40px;
  }
}
.owned-media-contract-flow__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-contract-flow__container {
    max-width: 100%;
  }
}
.owned-media-contract-flow__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-contract-flow__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-contract-flow__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-contract-flow__subtitle::before, .owned-media-contract-flow__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-contract-flow__subtitle::before {
  margin-right: 8px;
}
.owned-media-contract-flow__subtitle::after {
  margin-left: 8px;
}
.owned-media-contract-flow__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-contract-flow__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-contract-flow__title {
    font-size: 28px;
  }
  .owned-media-contract-flow__subtitle {
    gap: 4px;
  }
  .owned-media-contract-flow__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-contract-flow__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-contract-flow__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-contract-flow__content {
  display: flex;
  flex-direction: column;
  gap: 32px;
  align-items: center;
  width: 100%;
}
.owned-media-contract-flow .flow-step {
  position: relative;
  display: flex;
  flex: 1;
  gap: 32px;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  min-height: 162px;
  padding: 22px;
  padding-left: 0;
  background-color: #fff;
  filter: drop-shadow(5px 5px 8px rgba(0, 0, 0, 0.3));
  border-radius: 20px;
}
.owned-media-contract-flow .flow-step::after {
  position: absolute;
  bottom: -14px;
  left: 48%;
  width: 28px;
  height: 28px;
  content: "";
  background-color: #fff;
  transform: rotate(135deg) skew(20deg, 20deg);
}
.owned-media-contract-flow .flow-step__number {
  flex-shrink: 0;
  padding: 28px 32px;
  font-family: "Yu Gothic", "YuGothic", "Hiragino Kaku Gothic ProN", "Hiragino Sans", meiryo, sans-serif;
  font-size: 40px;
  font-size: 40px;
  font-weight: 700;
  color: #fff;
  text-align: center;
  white-space: nowrap;
  background-color: #7fd5bf;
  border-radius: 0 75px 75px 0;
}
.owned-media-contract-flow .flow-step__description {
  display: flex;
  flex: 1;
  flex-direction: column;
  gap: 24px;
  color: #5f6061;
}
.owned-media-contract-flow .flow-step__description-title {
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  color: #5f6061;
}
.owned-media-contract-flow .flow-step__description-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 16px;
  font-weight: 400;
  line-height: normal;
}
.owned-media-contract-flow .flow-step__icon {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: auto;
  max-width: 140px;
  height: auto;
  max-height: 100%;
}
.owned-media-contract-flow .flow-step__icon img {
  width: 100%;
  height: 100%;
  object-fit: contain;
}
@media (max-width: 768px) {
  .owned-media-contract-flow {
    padding: 60px 0;
  }
  .owned-media-contract-flow__container {
    padding: 0 20px;
  }
  .owned-media-contract-flow__header {
    margin-bottom: 60px;
  }
  .owned-media-contract-flow__content {
    gap: 40px;
  }
  .owned-media-contract-flow .flow-step {
    flex-direction: column;
    gap: 30px;
    align-items: center;
    min-height: auto;
    padding: 35px 20px;
    overflow: hidden;
    border-radius: 8px;
    box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.08);
  }
  .owned-media-contract-flow .flow-step::after {
    z-index: 1;
  }
  .owned-media-contract-flow .flow-step__number {
    position: absolute;
    top: -60px;
    left: -60px;
    width: 120px;
    height: 120px;
    padding: 15px 20px;
    font-size: clamp(40px, 8vw, 60px);
    border-radius: 50%;
  }
  .owned-media-contract-flow .flow-step__number-text {
    position: absolute;
    right: 20px;
    bottom: 20px;
    font-size: 28px;
    color: #fff;
  }
  .owned-media-contract-flow .flow-step__description {
    gap: 20px;
    text-align: center;
  }
  .owned-media-contract-flow .flow-step__description-title {
    font-size: clamp(28px, 6vw, 36px);
    line-height: 1.3;
  }
  .owned-media-contract-flow .flow-step__description-text {
    font-size: 20px;
    line-height: 1.5;
  }
  .owned-media-contract-flow .flow-step__icon {
    width: 120px;
    height: auto;
  }
}

.owned-media-faq {
  width: 100%;
  padding: 60px 0 100px;
  background-color: #fff;
}
@media (max-width: 768px) {
  .owned-media-faq {
    padding: 80px 0 40px;
  }
}
.owned-media-faq__container {
  position: relative;
  display: flex;
  flex-direction: column;
  width: 100%;
  max-width: 1112px;
  padding: 0 16px;
  margin: 0 auto;
}
@media (max-width: 768px) {
  .owned-media-faq__container {
    max-width: 100%;
  }
}
.owned-media-faq__header {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 24px;
  align-items: center;
  margin-bottom: 80px;
  text-align: center;
}
.owned-media-faq__title {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: clamp(52px, 10vw, 60px);
  font-weight: 700;
  color: #3ab795;
}
.owned-media-faq__subtitle {
  position: relative;
  display: flex;
  gap: 12px;
  align-items: center;
  justify-content: center;
}
.owned-media-faq__subtitle::before, .owned-media-faq__subtitle::after {
  display: inline-block;
  width: 15px;
  height: 4px;
  content: "";
  background-color: #3ab795;
  border-radius: 2px;
}
.owned-media-faq__subtitle::before {
  margin-right: 8px;
}
.owned-media-faq__subtitle::after {
  margin-left: 8px;
}
.owned-media-faq__subtitle-text {
  font-family: "Noto Sans JP", helvetica, sans-serif;
  font-size: 28px;
  font-weight: 500;
  line-height: 1;
  color: #3ab795;
  letter-spacing: 0.38px;
}
@media (max-width: 768px) {
  .owned-media-faq__header {
    gap: 12px;
    margin-bottom: 48px;
  }
  .owned-media-faq__title {
    font-size: 28px;
  }
  .owned-media-faq__subtitle {
    gap: 4px;
  }
  .owned-media-faq__subtitle::before {
    height: 2px;
    margin-right: 0;
  }
  .owned-media-faq__subtitle::after {
    height: 2px;
    margin-left: 0;
  }
  .owned-media-faq__subtitle-text {
    font-size: 24px;
  }
}
.owned-media-faq .faq__list {
  display: flex;
  flex-direction: column;
}
.owned-media-faq .faq__item {
  display: flex;
  flex-direction: column;
  gap: 32px;
  padding: 40px 0;
  border-bottom: 1px solid #a6bfc3;
}
.owned-media-faq .faq__item .faq-text-wrap {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}
.owned-media-faq .faq__item .faq-text-wrap.answer {
  padding-left: 72px;
}
.owned-media-faq .faq__item .faq-text.question {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}
.owned-media-faq .faq__item .faq-text.question::before {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  font-size: 28px;
  font-weight: 900;
  line-height: 1;
  color: #fff;
  content: "Q";
  background-color: #3ab795;
  border-radius: 7px;
}
.owned-media-faq .faq__item .faq-text.answer {
  display: flex;
  gap: 20px;
  align-items: flex-start;
  font-family: "Noto Sans JP", helvetica, sans-serif;
}
.owned-media-faq .faq__item .faq-text.answer::before {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 46px;
  height: 46px;
  font-size: 28px;
  font-weight: 900;
  line-height: 1;
  color: #fff;
  content: "A";
  background-color: #a6bfc3;
  border-radius: 7px;
}
.owned-media-faq .faq__item .faq-text-text.question {
  font-size: 24px;
  font-weight: 700;
  line-height: normal;
  color: #5f6061;
}
.owned-media-faq .faq__item .faq-text-text.answer {
  font-size: 16px;
  font-weight: 400;
  line-height: 1.5;
  color: #333;
}
@media (max-width: 768px) {
  .owned-media-faq {
    padding: 60px 0 40px;
  }
  .owned-media-faq__container {
    padding: 0 20px;
  }
  .owned-media-faq__header {
    margin-bottom: 40px;
  }
  .owned-media-faq__title {
    font-size: clamp(32px, 8vw, 40px);
    letter-spacing: -0.5px;
  }
  .owned-media-faq__subtitle {
    font-size: clamp(18px, 4vw, 20px);
    letter-spacing: 0.2px;
  }
  .owned-media-faq .faq__item {
    gap: 30px;
    padding: 30px 0;
    border-bottom: 1px solid #a6bfc3;
  }
  .owned-media-faq .faq__item .faq-text {
    gap: 15px;
  }
  .owned-media-faq .faq__item .faq-text-wrap {
    gap: 15px;
  }
  .owned-media-faq .faq__item .faq-text-wrap.answer {
    padding-left: 0;
  }
  .owned-media-faq .faq__item .faq-text::before {
    width: 36px;
    height: 36px;
    font-size: 20px;
    border-radius: 5px;
  }
  .owned-media-faq .faq__item .faq-text-text.question {
    font-size: clamp(18px, 4.5vw, 16px);
    font-weight: 700;
    line-height: 1.4;
    color: #5f6061;
  }
  .owned-media-faq .faq__item .faq-text-text.answer {
    font-size: clamp(16px, 4vw, 18px);
    font-weight: 400;
    line-height: 1.6;
    color: #333;
  }
}

.owned-media-lp #owned-media-form {
  border-radius: 5px;
}

@media (max-width: 768px) {
  .owned-media-lp #owned-media-form {
    border-radius: 5px;
  }
}

/*# sourceMappingURL=owned-media.css.map */
