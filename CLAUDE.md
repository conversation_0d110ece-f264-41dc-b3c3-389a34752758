# Claude Code 実行プロンプト
ユーザーは音声入力を使用しているため、誤字・脱字については文脈から判断してください。判断がつかないものはユーザーに再度質問してください。
現在の設計思想を理解して正しい作業を心がけてください。
omniSearchを活用してベストプラクティスを確認しながら作業を実施してください。
ユーザーからの指示を適切にMCPのタスクマスターを利用して、タスクとして展開して作業を実施してください。
「おそらく～」「～してみましょう」のような基準で作業をすることは禁止です。

## 作業対象
### wordpress HTML
/home/<USER>/projects/wordpress-appmart/mount_wordpress/wp-content/themes/appmart/s-owned-media.php

### scss(セクション毎にファイル分離)
/home/<USER>/projects/wordpress-appmart/mount_wordpress/wp-content/themes/appmart/assets/css/owned-media

### javascript
/home/<USER>/projects/wordpress-appmart/mount_wordpress/wp-content/themes/appmart/assets/js/owned-media.js

## 参考情報
MCPのFigmaを利用して実際のデザインを確認することができます。
次のURLが実際のFigmaデザインのノードです。ファイルサイズが大きいので、必要なセクションを特定し、そのセクションの情報を確実に読み込むようにしてください。
想像やあなたの知識に依存した妄想で、独断による修正作業を行わないでください。指示されたことを実装することに集中してください。

### PC版デザイン
https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=83-433&t=ZWwumTda9018zlAf-0

### スマホ版デザイン
https://www.figma.com/design/wTDUbl55LjN8R9NzeiZppa/Appmart_owned-media?node-id=83-4651&t=ZWwumTda9018zlAf-0
