// 最初の要素のスタイル取得
const element1 = document.querySelector('span.owned-media-empathy__subtitle-text.u-underline.u-underline--mint');
const element2 = document.querySelector('span.owned-media-empathy__title-text.u-underline.u-underline--mint');

function getElementStyles(el) {
  if (\!el) return null;
  
  const computed = window.getComputedStyle(el);
  const beforeComputed = window.getComputedStyle(el, '::before');
  const afterComputed = window.getComputedStyle(el, '::after');
  
  return {
    element: {
      position: computed.position,
      display: computed.display,
      fontSize: computed.fontSize,
      height: computed.height,
      className: el.className,
      tagName: el.tagName
    },
    before: {
      content: beforeComputed.content,
      position: beforeComputed.position,
      bottom: beforeComputed.bottom,
      left: beforeComputed.left,
      width: beforeComputed.width,
      height: beforeComputed.height,
      backgroundColor: beforeComputed.backgroundColor,
      borderRadius: beforeComputed.borderRadius,
      zIndex: beforeComputed.zIndex
    },
    after: {
      content: afterComputed.content,
      position: afterComputed.position,
      bottom: afterComputed.bottom,
      left: afterComputed.left,
      width: afterComputed.width,
      height: afterComputed.height,
      backgroundColor: afterComputed.backgroundColor,
      borderRadius: afterComputed.borderRadius,
      zIndex: afterComputed.zIndex
    }
  };
}

const styles1 = getElementStyles(element1);
const styles2 = getElementStyles(element2);

console.log('Element 1 Styles:', styles1);
console.log('Element 2 Styles:', styles2);

return { element1: styles1, element2: styles2 };
EOF < /dev/null
