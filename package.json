{"scripts": {"start": "browser-sync start --config bs-config.js", "sass:compile": "sass mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.css --style=expanded", "sass:watch": "sass mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.css --watch --style=expanded & browser-sync start --config bs-config.js", "build:owned-media": "webpack --config webpack.config.js", "watch:owned-media": "webpack --config webpack.config.js --watch"}, "devDependencies": {"@prettier/plugin-php": "^0.22.1", "autoprefixer": "^10.4.21", "browser-sync": "^3.0.2", "css-loader": "^7.1.2", "eslint": "^8.15.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "install": "^0.13.0", "mini-css-extract-plugin": "^2.9.2", "npm": "^10.8.0", "postcss": "^8.4.31", "postcss-html": "^1.4.1", "postcss-loader": "^8.1.1", "prettier": "^3.2.5", "quokka-jquery-loader": "^0.0.3", "sass": "^1.89.2", "sass-loader": "^16.0.5", "stylelint": "^16.2.0", "stylelint-config-recess-order": "^4.4.0", "stylelint-config-sass-guidelines": "^11.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-config-standard-scss": "^13.0.0", "stylelint-prettier": "^5.0.0", "stylelint-scss": "^6.1.0", "stylelint-selector-bem-pattern": "^3.0.1", "webpack": "^5.99.9", "webpack-cli": "^6.0.1", "webpack-remove-empty-scripts": "^1.1.1"}, "dependencies": {"puppeteer": "^24.10.0"}}