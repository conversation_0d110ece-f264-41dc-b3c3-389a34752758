const path = require('path');
const MiniCssExtractPlugin = require('mini-css-extract-plugin');
const RemoveEmptyScriptsPlugin = require('webpack-remove-empty-scripts');

module.exports = {
  mode: 'development', // 本番用設定は不要とのことなので、開発モードで設定
  entry: {
    'owned-media': './mount_wordpress/wp-content/themes/appmart/assets/css/owned-media.scss'
  },
  output: {
    path: path.resolve(__dirname, 'mount_wordpress/wp-content/themes/appmart/assets/css'),
    filename: '[name].js' // JSファイルは生成されるが使用しない
  },
  module: {
    rules: [
      {
        test: /\.scss$/,
        use: [
          MiniCssExtractPlugin.loader,
          {
            loader: 'css-loader',
            options: {
              // 画像ファイルのURLをそのまま保持
              url: false
            }
          },
          {
            loader: 'postcss-loader',
            options: {
              postcssOptions: {
                plugins: [
                  ['autoprefixer', {
                    // ブラウザ互換性のための設定
                    overrideBrowserslist: ['last 2 versions', '> 1%']
                  }]
                ]
              }
            }
          },
          'sass-loader'
        ]
      }
    ]
  },
  plugins: [
    new RemoveEmptyScriptsPlugin(),
    new MiniCssExtractPlugin({
      filename: '[name].css'
    })
  ],
  // 不要なJSファイルの出力を抑制
  optimization: {
    removeEmptyChunks: true
  }
};